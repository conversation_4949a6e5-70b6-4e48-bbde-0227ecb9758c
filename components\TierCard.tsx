
import React from 'react';
import { MembershipTier } from '../types';
import { THEME_COLORS } from '../constants';
import { WalletIcon } from './icons/WalletIcon';

interface TierCardProps {
  tier: MembershipTier;
}

const TierCard: React.FC<TierCardProps> = ({ tier }) => {
  const isPrimaryGlow = tier.glowColor === 'purple'; // Purple for primary tiers
  const isSecondaryGlow = tier.glowColor === 'red'; // Red for secondary high-tier

  let titleColorClass = THEME_COLORS.textLight;
  let priceColorClass = THEME_COLORS.textLight;
  let borderColorClass = `${THEME_COLORS.borderPrimaryAccent}/30`;
  let shadowClass = `hover:${THEME_COLORS.shadowPrimary}/70`; // Default hover shadow
  let popularTagBg = THEME_COLORS.gradientBgButton;
  let popularTagText = 'text-white';
  let buttonClasses = `${THEME_COLORS.gradientBgButton} text-white ${THEME_COLORS.buttonGlow}`;


  if (isPrimaryGlow) {
    titleColorClass = THEME_COLORS.textPrimaryAccent;
    priceColorClass = THEME_COLORS.gradientTextUser; // More vibrant for price
    borderColorClass = `${THEME_COLORS.borderPrimaryAccent}/70`;
    shadowClass = tier.isPopular ? `${THEME_COLORS.shadowPrimary}` : `hover:${THEME_COLORS.shadowPrimary}/70`;
  } else if (isSecondaryGlow) {
    titleColorClass = THEME_COLORS.textSecondaryAccent;
    priceColorClass = THEME_COLORS.textSecondaryAccent; // Solid red
    borderColorClass = `${THEME_COLORS.borderSecondaryAccent}/70`;
    shadowClass = tier.isPopular ? `${THEME_COLORS.shadowSecondary}` : `hover:${THEME_COLORS.shadowSecondary}/70`;
    buttonClasses = `bg-[var(--color-secondary-red)] hover:bg-[var(--color-gradient-pink)] text-white ${THEME_COLORS.buttonGlowSecondary}`;
  } else { // Neutral/Free tier
     borderColorClass = `border-[var(--color-text-dark)]/50`;
     buttonClasses = `bg-[var(--color-text-dark)]/30 hover:bg-[var(--color-text-dark)]/50 ${THEME_COLORS.textMedium} border-[var(--color-text-dark)]/50`;
     popularTagBg = `bg-[var(--color-text-medium)]`;
     popularTagText = `text-[var(--color-dark-bg)]`;
  }


  return (
    <div className={`${THEME_COLORS.bgGlass} rounded-xl p-5 flex flex-col h-full border-2 ${borderColorClass} shadow-xl ${shadowClass} transition-all duration-300 hover:scale-105 scroll-animation relative`}> {/* Compacted p-6 to p-5 */}
      {tier.isPopular && (
        <div className={`absolute -top-2.5 -right-2.5 ${popularTagBg} ${popularTagText} text-2xs font-bold px-2.5 py-1 rounded-full shadow-md transform rotate-3`}> {/* Compacted text, padding */}
          ALPHA ACCESS
        </div>
      )}
      <h3 className={`text-xl font-bold mb-1.5 ${titleColorClass}`}>{tier.name}</h3> {/* Compacted text-2xl, mb-2 */}
      <div className="mb-3"> {/* Compacted mb-4 */}
        <span className={`text-3xl font-extrabold ${priceColorClass}`}> {/* Compacted text-4xl */}
          {typeof tier.priceUSD === 'string' ? tier.priceUSD : `$${tier.priceUSD}`}
        </span>
        {typeof tier.priceUSD !== 'string' && <span className={`${THEME_COLORS.textDark} text-xs`}>/moon cycle</span>} {/* Compacted text-sm */}
      </div>
      {tier.priceCrypto && (
        <p className={`text-xs ${THEME_COLORS.textMedium} mb-3`}> {/* Compacted text-sm, mb-4 */}
          or {tier.priceCrypto.amount} {tier.priceCrypto.currency}
          <span className={`text-2xs ${THEME_COLORS.textDark} ml-1`}> (est. L2 gas ~$0.42)</span> {/* Compacted text-xs */}
        </p>
      )}
      <ul className={`space-y-1.5 text-xs ${THEME_COLORS.textMedium} mb-5 flex-grow`}> {/* Compacted space-y-2, text-sm, mb-6 */}
        {tier.features.map((feature, index) => (
          <li key={index} className="flex items-start">
            <svg className={`w-3 h-3 mr-1.5 mt-0.5 flex-shrink-0 ${isPrimaryGlow ? THEME_COLORS.textPrimaryAccent : isSecondaryGlow ? THEME_COLORS.textSecondaryAccent : THEME_COLORS.textMedium }`} fill="currentColor" viewBox="0 0 20 20"> {/* Compacted w-4 h-4, mr-2 */}
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
            </svg>
            {feature}
          </li>
        ))}
      </ul>
      <button className={`w-full ${buttonClasses} font-semibold py-2.5 px-3 border border-transparent rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-1.5 text-sm`}> {/* Compacted py-3, space-x-2 */}
        {typeof tier.priceUSD === 'string' ? 'Enter the Nebula' : 'Stake for Singularity'}
        {tier.priceCrypto && <WalletIcon className={`w-4 h-4 ${isPrimaryGlow || isSecondaryGlow ? 'text-white/80' : THEME_COLORS.textDark}`}/>} {/* Compacted w-5 h-5 */}
      </button>
    </div>
  );
};

export default TierCard;
