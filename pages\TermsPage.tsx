
import React from 'react';
import { THEME_COLORS } from '../constants';

const TermsPage: React.FC = () => {
  return (
    <div className={`max-w-2xl mx-auto ${THEME_COLORS.bgGlass} p-5 md:p-8 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/20 scroll-animation`}> {/* Compacted max-w-3xl, p-6/10 */}
      <h1 className={`text-2xl md:text-3xl font-bold ${THEME_COLORS.gradientTextUser} mb-5`}>Terms of Service Protocol</h1> {/* Compacted text-3xl/4xl, mb-6 */}
      
      <div className={`space-y-3.5 ${THEME_COLORS.textMedium} text-sm`}> {/* Compacted space-y-4 */}
        <p>Welcome to JetSharing.co (the "Platform"). These Terms of Service ("Terms") govern your access to and use of our platform and any associated services or decentralized applications (dApps). By interfacing with the Platform, you consent to be bound by these Terms. If you dissent, please disconnect from the Platform.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>1. Platform Symbiosis</h2> {/* Compacted text-xl, mt-6, mb-2 */}
        <p>JetSharing.co functions as a technology nexus, linking individuals seeking private air transit ("Travelers") with pilots or aircraft operators ("Pilots") offering capacity on flights, often "empty leg" vectors. We are not an aircraft operator nor an air carrier. All flight agreements are peer-to-peer between Travelers and Pilots. Users are urged to conduct their own due diligence (DYOR).</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>2. User Obligations & Conduct</h2>
        <p>Users agree to provide verifiably accurate information. Pilots bear sole responsibility for all necessary licenses, certifications, insurance, and adherence to all aviation regulations. Travelers are responsible for their diligence in selecting flights and Pilots. </p>
        
        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>3. Payments & Crypto-Logistics</h2>
        <p>The Platform may facilitate payments, including those via cryptocurrencies. Users acknowledge the inherent volatility and risks of digital assets. Blockchain-confirmed transactions are generally immutable. JetSharing.co is not liable for crypto value fluctuations or network transaction (gas) fees.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>4. Prohibited Network Activity</h2>
        <p>Users shall not utilize the Platform for illicit activities, misrepresentation of identity or services, or to disrupt Platform operations. Any conduct deemed inappropriate may result in network access suspension or termination.</p>
        
        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>5. Disclaimers & Liability Attenuation</h2>
        <p>THE PLATFORM IS PROVIDED "AS IS" AND "AS AVAILABLE." JETSHARING.CO DISCLAIMS ALL LIABILITY FOR DAMAGES ARISING FROM PLATFORM USE, INCLUDING ISSUES OF FLIGHT SAFETY, CANCELLATIONS, OR INTER-USER DISPUTES. MAXIMUM LIABILITY IS LIMITED TO SERVICE FEES PAID TO JETSHARING.CO, IF ANY.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>6. Protocol Amendments</h2>
        <p>We reserve the right to amend these Terms. Continued Platform use post-amendment signifies acceptance of new Terms. Review Terms periodically.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>7. Governing Network State</h2>
        <p>These Terms are governed by the laws of the jurisdiction of JetSharing.co's primary operational node, irrespective of conflict of law provisions.</p>

        <p className={`mt-6 text-xs ${THEME_COLORS.textDark}`}>Last Updated: {new Date().toLocaleDateString()}</p> {/* Compacted mt-8, text-sm */}
      </div>
    </div>
  );
};

export default TermsPage;
