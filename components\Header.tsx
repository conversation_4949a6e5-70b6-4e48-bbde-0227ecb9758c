
import React, { useState } from 'react';
import { Link, NavLink, useNavigate } from 'react-router-dom';
import { ROUTES, THEME_COLORS, NAV_LINKS_CONFIG } from '../constants';
import { useWallet } from '../contexts/WalletContext';
import { useAuth } from '../contexts/AuthContext';
import { WalletIcon } from './icons/WalletIcon';
import { UserRole } from '../types';

const NavItem: React.FC<{ to: string; children: React.ReactNode }> = ({ to, children }) => (
  <NavLink
    to={to}
    className={({ isActive }) =>
      `px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-300 transform hover:scale-105 ${ // Compacted py
        isActive
          ? `${THEME_COLORS.gradientTextUser} bg-[var(--color-glass-bg)] border ${THEME_COLORS.borderPrimaryAccent}/50 shadow-md`
          : `${THEME_COLORS.textMedium} hover:${THEME_COLORS.textLight} hover:bg-[var(--color-glass-bg)]/70`
      }`
    }
  >
    {children}
  </NavLink>
);

const Header: React.FC = () => {
  const { isConnected: isWalletConnected, address: walletAddress, connectWallet, disconnectWallet } = useWallet();
  const { isAuthenticated, currentUser, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    setIsMobileMenuOpen(false);
    navigate(ROUTES.HOME);
  };
  
  const getDashboardPath = () => {
    if (currentUser?.role === UserRole.Pilot) {
      return ROUTES.PILOT_DASHBOARD;
    }
    return ROUTES.TRAVELER_DASHBOARD;
  };

  const ActionButton: React.FC<{ onClick?: () => void; to?: string; children: React.ReactNode; primary?: boolean; className?: string }> = ({ onClick, to, children, primary = false, className = '' }) => {
    const baseClasses = `font-semibold py-2 px-3 border rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-xs flex items-center space-x-2 ${THEME_COLORS.buttonGlow}`;
    const primaryClasses = `${primary ? `${THEME_COLORS.gradientBgButton} text-white ${THEME_COLORS.borderPrimaryAccent}/30` : `${THEME_COLORS.bgGlass} ${THEME_COLORS.textPrimaryAccent} ${THEME_COLORS.borderPrimaryAccent}/70 hover:border-[var(--color-secondary-red)] hover:text-[var(--color-secondary-red)]`}`;
    
    if (to) {
      return <Link to={to} className={`${baseClasses} ${primaryClasses} ${className}`}>{children}</Link>;
    }
    return <button onClick={onClick} className={`${baseClasses} ${primaryClasses} ${className}`}>{children}</button>;
  };


  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 ${THEME_COLORS.bgGlass} shadow-lg shadow-black/70`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16"> {/* Compacted h-20 to h-16 */}
          <div className="flex items-center">
            <Link to={ROUTES.HOME} className={`text-2xl font-bold ${THEME_COLORS.gradientTextUser} hover:opacity-80 transition-opacity`}> {/* Compacted text-3xl */}
              JetSharing.co
            </Link>
          </div>
          <div className="hidden md:flex items-center space-x-3"> {/* Compacted space-x-4 */}
            <NavItem to={ROUTES.SEARCH_FLIGHTS}>{NAV_LINKS_CONFIG.findFlights}</NavItem>
            <NavItem to={ROUTES.LIST_LEG}>{NAV_LINKS_CONFIG.listLeg}</NavItem>
            <NavItem to={ROUTES.MEMBERSHIP}>{NAV_LINKS_CONFIG.membership}</NavItem>
            <NavItem to={ROUTES.MEMBERS_DIRECTORY}>{NAV_LINKS_CONFIG.directory}</NavItem>
            {isAuthenticated && currentUser && (
              <NavItem to={getDashboardPath()}>{NAV_LINKS_CONFIG.dashboard}</NavItem>
            )}
          </div>
          <div className="hidden md:flex items-center space-x-3"> {/* Compacted space-x-4 */}
            {isAuthenticated && currentUser ? (
              <>
                <span className={`text-xs ${THEME_COLORS.textMedium}`}>Hi, {currentUser.name.split(' ')[0]}!</span>
                {isWalletConnected && walletAddress && (
                   <span className="text-xs text-green-400">{walletAddress.substring(0, 5)}...{walletAddress.substring(walletAddress.length - 3)} <span className={`${THEME_COLORS.textSecondaryAccent}`}>(Synced)</span></span>
                )}
                <ActionButton onClick={handleLogout} className="bg-red-700/50 hover:bg-red-600/70 text-[var(--color-text-light)] border-red-500/50">
                  Logout
                </ActionButton>
              </>
            ) : (
              <>
                <ActionButton to={ROUTES.LOGIN} primary>
                   <span>{NAV_LINKS_CONFIG.login}</span>
                </ActionButton>
                {!isWalletConnected && (
                    <ActionButton onClick={connectWallet}>
                      <WalletIcon className="w-4 h-4" />
                      <span>{NAV_LINKS_CONFIG.connectWallet}</span>
                    </ActionButton>
                )}
                 {isWalletConnected && walletAddress && (
                    <div className="flex items-center space-x-2">
                        <span className="text-xs text-green-400">{walletAddress.substring(0, 5)}...{walletAddress.substring(walletAddress.length - 3)} <span className={`${THEME_COLORS.textSecondaryAccent}`}>(Synced)</span></span>
                        <ActionButton onClick={disconnectWallet} className="bg-red-700/50 hover:bg-red-600/70 text-[var(--color-text-light)] border-red-500/50">
                          Disconnect
                        </ActionButton>
                    </div>
                )}
              </>
            )}
          </div>
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`p-1.5 rounded-md ${THEME_COLORS.textPrimaryAccent} hover:bg-[var(--color-glass-bg)] focus:outline-none focus:ring-2 focus:ring-inset ${THEME_COLORS.ringPrimaryAccent}`}
            > {/* Compacted p-2 */}
              <svg className="h-5 w-5" stroke="currentColor" fill="none" viewBox="0 0 24 24"> {/* Compacted h-6 w-6 */}
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>
      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className={`md:hidden ${THEME_COLORS.bgGlass} border-t border-[var(--color-glass-border)]`}>
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <NavItem to={ROUTES.SEARCH_FLIGHTS}>{NAV_LINKS_CONFIG.findFlights}</NavItem>
            <NavItem to={ROUTES.LIST_LEG}>{NAV_LINKS_CONFIG.listLeg}</NavItem>
            <NavItem to={ROUTES.MEMBERSHIP}>{NAV_LINKS_CONFIG.membership}</NavItem>
            <NavItem to={ROUTES.MEMBERS_DIRECTORY}>{NAV_LINKS_CONFIG.directory}</NavItem>
            {isAuthenticated && currentUser && (
              <NavItem to={getDashboardPath()}>{NAV_LINKS_CONFIG.dashboard}</NavItem>
            )}
            <div className={`pt-3 border-t border-[var(--color-glass-border)]/70 mt-2`}>
            {isAuthenticated && currentUser ? (
              <div className="flex flex-col items-start space-y-2 p-2">
                <span className={`text-xs ${THEME_COLORS.textMedium}`}>Welcome, {currentUser.name.split(' ')[0]}</span>
                 {isWalletConnected && walletAddress && (
                   <span className="text-xs text-green-400">{walletAddress.substring(0, 5)}...{walletAddress.substring(walletAddress.length - 3)} <span className={`${THEME_COLORS.textSecondaryAccent}`}>(Synced)</span></span>
                )}
                <ActionButton onClick={handleLogout} className="w-full text-left bg-red-700/50 hover:bg-red-600/70 text-[var(--color-text-light)] border-red-500/50 !text-sm">
                  Logout
                </ActionButton>
              </div>
            ) : (
              <div className="space-y-2 p-1">
                <ActionButton to={ROUTES.LOGIN} primary onClick={() => setIsMobileMenuOpen(false)} className="w-full justify-center !text-sm">
                   <span>{NAV_LINKS_CONFIG.login}</span>
                </ActionButton>
                {!isWalletConnected && (
                    <ActionButton onClick={() => { connectWallet(); setIsMobileMenuOpen(false); }} className="w-full justify-center !text-sm">
                      <WalletIcon className="w-4 h-4" />
                      <span>{NAV_LINKS_CONFIG.connectWallet}</span>
                    </ActionButton>
                )}
                 {isWalletConnected && walletAddress && (
                    <div className="flex flex-col items-start space-y-1.5 p-1">
                        <span className="text-xs text-green-400">{walletAddress.substring(0, 5)}...{walletAddress.substring(walletAddress.length - 3)} <span className={`${THEME_COLORS.textSecondaryAccent}`}>(Synced)</span></span>
                        <ActionButton onClick={() => { disconnectWallet(); setIsMobileMenuOpen(false); }} className="w-full text-left bg-red-700/50 hover:bg-red-600/70 text-[var(--color-text-light)] border-red-500/50 !text-xs">
                          Disconnect Wallet
                        </ActionButton>
                    </div>
                )}
              </div>
            )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Header;
