
import React, { useState } from 'react';
import { Link, NavLink, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ROUTES, THEME_COLORS, NAV_LINKS_CONFIG } from '../constants';
import { useWallet } from '../contexts/WalletContext';
import { useAuth } from '../contexts/AuthContext';
import { WalletIcon } from './icons/WalletIcon';
import { UserRole } from '../types';
import { Menu, X, Zap, User, LogOut } from 'lucide-react';
import AnimatedButton from './AnimatedButton';

const NavItem: React.FC<{ to: string; children: React.ReactNode }> = ({ to, children }) => (
  <NavLink
    to={to}
    className={({ isActive }) =>
      `relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 group ${
        isActive
          ? 'text-white bg-gradient-to-r from-purple-500 to-pink-500 shadow-lg'
          : 'text-gray-300 hover:text-white hover:bg-white/10'
      }`
    }
  >
    {({ isActive }) => (
      <>
        {children}
        {!isActive && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg opacity-0 group-hover:opacity-100"
            initial={{ scale: 0.8 }}
            whileHover={{ scale: 1 }}
            transition={{ duration: 0.2 }}
          />
        )}
      </>
    )}
  </NavLink>
);

const Header: React.FC = () => {
  const { isConnected: isWalletConnected, address: walletAddress, connectWallet, disconnectWallet } = useWallet();
  const { isAuthenticated, currentUser, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    setIsMobileMenuOpen(false);
    navigate(ROUTES.HOME);
  };
  
  const getDashboardPath = () => {
    if (currentUser?.role === UserRole.Pilot) {
      return ROUTES.PILOT_DASHBOARD;
    }
    return ROUTES.TRAVELER_DASHBOARD;
  };

  const ActionButton: React.FC<{ onClick?: () => void; to?: string; children: React.ReactNode; primary?: boolean; className?: string }> = ({ onClick, to, children, primary = false, className = '' }) => {
    const baseClasses = `font-semibold py-2 px-3 border rounded-lg shadow-md hover:shadow-lg transition-all duration-300 text-xs flex items-center space-x-2 ${THEME_COLORS.buttonGlow}`;
    const primaryClasses = `${primary ? `${THEME_COLORS.gradientBgButton} text-white ${THEME_COLORS.borderPrimaryAccent}/30` : `${THEME_COLORS.bgGlass} ${THEME_COLORS.textPrimaryAccent} ${THEME_COLORS.borderPrimaryAccent}/70 hover:border-[var(--color-secondary-red)] hover:text-[var(--color-secondary-red)]`}`;
    
    if (to) {
      return <Link to={to} className={`${baseClasses} ${primaryClasses} ${className}`}>{children}</Link>;
    }
    return <button onClick={onClick} className={`${baseClasses} ${primaryClasses} ${className}`}>{children}</button>;
  };


  return (
    <motion.nav
      className="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-black/20 border-b border-purple-500/20"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-18">
          <motion.div
            className="flex items-center"
            whileHover={{ scale: 1.05 }}
            transition={{ type: 'spring', stiffness: 400 }}
          >
            <Link
              to={ROUTES.HOME}
              className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent hover:from-purple-300 hover:via-pink-300 hover:to-red-300 transition-all duration-300"
            >
              JetSharing.co
            </Link>
          </motion.div>
          <motion.div
            className="hidden md:flex items-center space-x-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, staggerChildren: 0.1 }}
          >
            <NavItem to={ROUTES.SEARCH_FLIGHTS}>{NAV_LINKS_CONFIG.findFlights}</NavItem>
            <NavItem to={ROUTES.LIST_LEG}>{NAV_LINKS_CONFIG.listLeg}</NavItem>
            <NavItem to={ROUTES.MEMBERSHIP}>{NAV_LINKS_CONFIG.membership}</NavItem>
            <NavItem to={ROUTES.MEMBERS_DIRECTORY}>{NAV_LINKS_CONFIG.directory}</NavItem>
            {isAuthenticated && currentUser && (
              <NavItem to={getDashboardPath()}>{NAV_LINKS_CONFIG.dashboard}</NavItem>
            )}
          </motion.div>
          <motion.div
            className="hidden md:flex items-center space-x-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {isAuthenticated && currentUser ? (
              <>
                <motion.span
                  className="text-sm text-gray-300"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                >
                  Hi, {currentUser.name.split(' ')[0]}!
                </motion.span>
                {isWalletConnected && walletAddress && (
                  <motion.div
                    className="flex items-center gap-2 px-3 py-1 rounded-full bg-green-500/20 border border-green-500/30"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <Zap className="w-3 h-3 text-green-400" />
                    <span className="text-xs text-green-400">
                      {walletAddress.substring(0, 5)}...{walletAddress.substring(walletAddress.length - 3)}
                    </span>
                  </motion.div>
                )}
                <AnimatedButton
                  variant="ghost"
                  size="sm"
                  icon={<LogOut className="w-4 h-4" />}
                  onClick={handleLogout}
                >
                  Logout
                </AnimatedButton>
              </>
            ) : (
              <>
                <AnimatedButton
                  variant="primary"
                  size="sm"
                  icon={<User className="w-4 h-4" />}
                  onClick={() => window.location.href = `#${ROUTES.LOGIN}`}
                >
                  {NAV_LINKS_CONFIG.login}
                </AnimatedButton>
                {!isWalletConnected ? (
                  <AnimatedButton
                    variant="secondary"
                    size="sm"
                    icon={<Zap className="w-4 h-4" />}
                    onClick={connectWallet}
                  >
                    {NAV_LINKS_CONFIG.connectWallet}
                  </AnimatedButton>
                ) : (
                  <motion.div
                    className="flex items-center gap-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    <motion.div
                      className="flex items-center gap-2 px-3 py-1 rounded-full bg-green-500/20 border border-green-500/30"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: 'spring', stiffness: 300 }}
                    >
                      <Zap className="w-3 h-3 text-green-400" />
                      <span className="text-xs text-green-400">
                        {walletAddress?.substring(0, 5)}...{walletAddress?.substring(walletAddress.length - 3)}
                      </span>
                    </motion.div>
                    <AnimatedButton
                      variant="ghost"
                      size="sm"
                      onClick={disconnectWallet}
                    >
                      Disconnect
                    </AnimatedButton>
                  </motion.div>
                )}
              </>
            )}
          </motion.div>
          <div className="md:hidden flex items-center">
            <motion.button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-lg text-purple-400 hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-purple-500"
              whileTap={{ scale: 0.95 }}
            >
              <AnimatePresence mode="wait">
                {isMobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="h-6 w-6" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="h-6 w-6" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Enhanced Mobile menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="md:hidden backdrop-blur-xl bg-black/30 border-t border-purple-500/20"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            <motion.div
              className="px-4 pt-4 pb-6 space-y-3"
              initial={{ y: -20 }}
              animate={{ y: 0 }}
              exit={{ y: -20 }}
              transition={{ delay: 0.1 }}
            >
              <NavItem to={ROUTES.SEARCH_FLIGHTS}>{NAV_LINKS_CONFIG.findFlights}</NavItem>
              <NavItem to={ROUTES.LIST_LEG}>{NAV_LINKS_CONFIG.listLeg}</NavItem>
              <NavItem to={ROUTES.MEMBERSHIP}>{NAV_LINKS_CONFIG.membership}</NavItem>
              <NavItem to={ROUTES.MEMBERS_DIRECTORY}>{NAV_LINKS_CONFIG.directory}</NavItem>
              {isAuthenticated && currentUser && (
                <NavItem to={getDashboardPath()}>{NAV_LINKS_CONFIG.dashboard}</NavItem>
              )}

              <div className="pt-4 border-t border-purple-500/20 mt-4">
                {isAuthenticated && currentUser ? (
                  <div className="space-y-3">
                    <span className="text-sm text-gray-300">
                      Welcome, {currentUser.name.split(' ')[0]}!
                    </span>
                    {isWalletConnected && walletAddress && (
                      <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-green-500/20 border border-green-500/30">
                        <Zap className="w-3 h-3 text-green-400" />
                        <span className="text-xs text-green-400">
                          {walletAddress.substring(0, 5)}...{walletAddress.substring(walletAddress.length - 3)}
                        </span>
                      </div>
                    )}
                    <AnimatedButton
                      variant="ghost"
                      size="sm"
                      fullWidth
                      icon={<LogOut className="w-4 h-4" />}
                      onClick={() => { handleLogout(); setIsMobileMenuOpen(false); }}
                    >
                      Logout
                    </AnimatedButton>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <AnimatedButton
                      variant="primary"
                      size="sm"
                      fullWidth
                      icon={<User className="w-4 h-4" />}
                      onClick={() => { window.location.href = `#${ROUTES.LOGIN}`; setIsMobileMenuOpen(false); }}
                    >
                      {NAV_LINKS_CONFIG.login}
                    </AnimatedButton>
                    {!isWalletConnected ? (
                      <AnimatedButton
                        variant="secondary"
                        size="sm"
                        fullWidth
                        icon={<Zap className="w-4 h-4" />}
                        onClick={() => { connectWallet(); setIsMobileMenuOpen(false); }}
                      >
                        {NAV_LINKS_CONFIG.connectWallet}
                      </AnimatedButton>
                    ) : (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-green-500/20 border border-green-500/30">
                          <Zap className="w-3 h-3 text-green-400" />
                          <span className="text-xs text-green-400">
                            {walletAddress?.substring(0, 5)}...{walletAddress?.substring(walletAddress.length - 3)}
                          </span>
                        </div>
                        <AnimatedButton
                          variant="ghost"
                          size="sm"
                          fullWidth
                          onClick={() => { disconnectWallet(); setIsMobileMenuOpen(false); }}
                        >
                          Disconnect
                        </AnimatedButton>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Header;
