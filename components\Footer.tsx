
import React from 'react';
import { Link } from 'react-router-dom';
import { ROUTES, THEME_COLORS, NAV_LINKS_CONFIG } from '../constants';

const FooterLink: React.FC<{ to: string; children: React.ReactNode }> = ({ to, children }) => (
  <Link to={to} className={`text-xs ${THEME_COLORS.textMedium} hover:${THEME_COLORS.textPrimaryAccent} transition-colors duration-300`}> {/* Compacted text-sm */}
    {children}
  </Link>
);

const Footer: React.FC = () => {
  return (
    <footer className={`${THEME_COLORS.bgGlass} border-t ${THEME_COLORS.borderPrimaryAccent}/30 mt-10 shadow-inner-top-purple`}> {/* Compacted mt-12 */}
      <div className="container mx-auto px-4 py-6"> {/* Compacted py-8 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"> {/* Compacted gap-8, mb-8 */}
          <div>
            <h3 className={`text-lg font-bold ${THEME_COLORS.gradientTextUser} mb-3`}>{/* Compacted text-xl, mb-4 */}
              JetSharing.co
            </h3>
            <p className={`text-xs ${THEME_COLORS.textDark}`}>Decentralized Aviation. Elevated Journeys.</p> {/* Compacted text-sm */}
          </div>
          <div>
            <h4 className={`text-base font-semibold ${THEME_COLORS.textLight} mb-3`}>{/* Compacted text-lg, mb-4 */}
              Navigate
            </h4>
            <ul className="space-y-1.5"> {/* Compacted space-y-2 */}
              <li><FooterLink to={ROUTES.SEARCH_FLIGHTS}>{NAV_LINKS_CONFIG.findFlights}</FooterLink></li>
              <li><FooterLink to={ROUTES.LIST_LEG}>{NAV_LINKS_CONFIG.listLeg}</FooterLink></li>
              <li><FooterLink to={ROUTES.MEMBERSHIP}>{NAV_LINKS_CONFIG.membership}</FooterLink></li>
              <li><FooterLink to={ROUTES.PET_TRAVEL}>Pet Codex</FooterLink></li>
            </ul>
          </div>
          <div>
            <h4 className={`text-base font-semibold ${THEME_COLORS.textLight} mb-3`}>{/* Compacted text-lg, mb-4 */}
              Legal & Lore
            </h4>
            <ul className="space-y-1.5"> {/* Compacted space-y-2 */}
              <li><FooterLink to={ROUTES.TERMS}>Terms of Service</FooterLink></li>
              <li><FooterLink to={ROUTES.PRIVACY}>Privacy Protocol</FooterLink></li>
              <li><FooterLink to={ROUTES.FAA_DISCLAIMER}>Aviation Edicts</FooterLink></li>
              <li><FooterLink to={ROUTES.CRYPTO_PAYMENTS}>Crypto Charters</FooterLink></li>
              <li><FooterLink to={ROUTES.SAFETY}>Safety & Verification</FooterLink></li>
            </ul>
          </div>
        </div>
        <div className={`border-t ${THEME_COLORS.borderPrimaryAccent}/20 pt-6 text-center`}> {/* Compacted pt-8 */}
          <p className={`text-xs ${THEME_COLORS.textDark}`}>&copy; {new Date().getFullYear()} JetSharing.co. Forging the future of flight, on-chain and beyond.</p> {/* Compacted text-sm */}
        </div>
      </div>
    </footer>
  );
};

export default Footer;
