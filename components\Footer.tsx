
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ROUTES, THEME_COLORS, NAV_LINKS_CONFIG } from '../constants';
import { Plane, Zap, Shield, Star, Github, Twitter, MessageCircle } from 'lucide-react';

const FooterLink: React.FC<{ to: string; children: React.ReactNode }> = ({ to, children }) => (
  <motion.div whileHover={{ x: 5 }} transition={{ type: 'spring', stiffness: 400 }}>
    <Link
      to={to}
      className="text-sm text-gray-400 hover:text-purple-400 transition-colors duration-300 hover:underline"
    >
      {children}
    </Link>
  </motion.div>
);

const SocialLink: React.FC<{ href: string; icon: React.ReactNode; label: string }> = ({ href, icon, label }) => (
  <motion.a
    href={href}
    target="_blank"
    rel="noopener noreferrer"
    className="p-3 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-400 hover:text-white hover:border-purple-400 transition-all duration-300"
    whileHover={{ scale: 1.1, rotate: 5 }}
    whileTap={{ scale: 0.95 }}
    aria-label={label}
  >
    {icon}
  </motion.a>
);

const Footer: React.FC = () => {
  return (
    <motion.footer
      className="relative mt-20 backdrop-blur-xl bg-black/20 border-t border-purple-500/20"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-purple-400 rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          {/* Brand Section */}
          <motion.div
            className="md:col-span-1"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
          >
            <motion.h3
              className="text-2xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent mb-4"
              whileHover={{ scale: 1.05 }}
            >
              JetSharing.co
            </motion.h3>
            <p className="text-gray-400 mb-6 leading-relaxed">
              Decentralized Aviation. Elevated Journeys. The future of private flight is here.
            </p>

            {/* Social Links */}
            <div className="flex gap-3">
              <SocialLink href="#" icon={<Github className="w-5 h-5" />} label="GitHub" />
              <SocialLink href="#" icon={<Twitter className="w-5 h-5" />} label="Twitter" />
              <SocialLink href="#" icon={<MessageCircle className="w-5 h-5" />} label="Discord" />
            </div>
          </motion.div>

          {/* Navigation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
          >
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Plane className="w-5 h-5 text-purple-400" />
              Navigate
            </h4>
            <ul className="space-y-3">
              <li><FooterLink to={ROUTES.SEARCH_FLIGHTS}>{NAV_LINKS_CONFIG.findFlights}</FooterLink></li>
              <li><FooterLink to={ROUTES.LIST_LEG}>{NAV_LINKS_CONFIG.listLeg}</FooterLink></li>
              <li><FooterLink to={ROUTES.MEMBERSHIP}>{NAV_LINKS_CONFIG.membership}</FooterLink></li>
              <li><FooterLink to={ROUTES.PET_TRAVEL}>Pet Codex</FooterLink></li>
            </ul>
          </motion.div>

          {/* Legal */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
          >
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Shield className="w-5 h-5 text-red-400" />
              Legal & Lore
            </h4>
            <ul className="space-y-3">
              <li><FooterLink to={ROUTES.TERMS}>Terms of Service</FooterLink></li>
              <li><FooterLink to={ROUTES.PRIVACY}>Privacy Protocol</FooterLink></li>
              <li><FooterLink to={ROUTES.FAA_DISCLAIMER}>Aviation Edicts</FooterLink></li>
              <li><FooterLink to={ROUTES.CRYPTO_PAYMENTS}>Crypto Charters</FooterLink></li>
              <li><FooterLink to={ROUTES.SAFETY}>Safety & Verification</FooterLink></li>
            </ul>
          </motion.div>

          {/* Web3 Features */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
          >
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Zap className="w-5 h-5 text-cyan-400" />
              Web3 Features
            </h4>
            <ul className="space-y-3">
              <li><FooterLink to={ROUTES.CRYPTO_PAYMENTS}>Crypto Payments</FooterLink></li>
              <li><FooterLink to={ROUTES.MEMBERS_DIRECTORY}>DAO Members</FooterLink></li>
              <li>
                <span className="text-sm text-gray-400">NFT Badges</span>
              </li>
              <li>
                <span className="text-sm text-gray-400">Smart Contracts</span>
              </li>
            </ul>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          className="border-t border-purple-500/20 pt-8 flex flex-col md:flex-row justify-between items-center gap-4"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5 }}
        >
          <p className="text-sm text-gray-400 text-center md:text-left">
            &copy; {new Date().getFullYear()} JetSharing.co. Forging the future of flight, on-chain and beyond.
          </p>

          <motion.div
            className="flex items-center gap-2 text-sm text-gray-400"
            whileHover={{ scale: 1.05 }}
          >
            <Star className="w-4 h-4 text-yellow-400" />
            <span>Built with ❤️ for the Web3 Aviation Community</span>
          </motion.div>
        </motion.div>
      </div>
    </motion.footer>
  );
};

export default Footer;
