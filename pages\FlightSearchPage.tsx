
import React, { useState, useMemo } from 'react';
import { Flight } from '../types';
import { MOCK_FLIGHTS, THEME_COLORS } from '../constants';
import FlightCard from '../components/FlightCard';
import { MapPinIcon } from '../components/icons/MapPinIcon';
import { CalendarDaysIcon } from '../components/icons/CalendarDaysIcon';
import { UserGroupIcon } from '../components/icons/UserGroupIcon';
import { CurrencyDollarIcon } from '../components/icons/CurrencyDollarIcon';
import { PetIcon } from '../components/icons/PetIcon';
import { ChevronDownIcon } from '../components/icons/ChevronDownIcon';


const InputField: React.FC<{ label: string; type?: string; placeholder: string; value: string; onChange: (e: React.ChangeEvent<HTMLInputElement>) => void; icon?: React.ReactNode}> = 
  ({label, type = "text", placeholder, value, onChange, icon}) => (
  <div className="flex-1 min-w-[180px]"> {/* Compacted min-w */}
    <label htmlFor={label} className={`block text-xs font-medium ${THEME_COLORS.textPrimaryAccent} mb-0.5`}>{label}</label> {/* Compacted text-sm, mb-1 */}
    <div className="relative">
      {icon && <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">{icon}</div>} {/* Compacted pl-3 */}
      <input
        type={type}
        id={label}
        name={label}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        className={`w-full p-2.5 text-sm ${icon ? 'pl-9' : ''} ${THEME_COLORS.bgGlass} rounded-lg border ${THEME_COLORS.borderPrimaryAccent}/50 focus:ring-2 ${THEME_COLORS.ringPrimaryAccent} focus:${THEME_COLORS.borderPrimaryAccent} outline-none transition-all ${THEME_COLORS.textLight} placeholder-[var(--color-text-dark)]`} // Compacted p-3
      />
    </div>
  </div>
);

const FlightSearchPage: React.FC = () => {
  const [departure, setDeparture] = useState('');
  const [arrival, setArrival] = useState('');
  const [date, setDate] = useState('');
  const [passengers, setPassengers] = useState('1');
  const [petFriendly, setPetFriendly] = useState(false);
  const [maxPrice, setMaxPrice] = useState('50000'); 
  const [sortBy, setSortBy] = useState<'cheapest' | 'fastest' | 'mostSeats' | 'pilotRating'>('cheapest');

  const filteredFlights = useMemo(() => {
    let flights = MOCK_FLIGHTS;
    if (departure) {
      flights = flights.filter(f => f.departureAirport.toLowerCase().includes(departure.toLowerCase()));
    }
    if (arrival) {
      flights = flights.filter(f => f.arrivalAirport.toLowerCase().includes(arrival.toLowerCase()));
    }
    if (date) { 
      const searchDate = new Date(date + "T00:00:00Z"); // Ensure UTC for date comparison
      flights = flights.filter(f => new Date(f.departureTime).toDateString() === searchDate.toDateString());
    }
    if (passengers) {
      flights = flights.filter(f => f.availableSeats >= parseInt(passengers));
    }
    if (petFriendly) {
      flights = flights.filter(f => f.isPetFriendly);
    }
    if (maxPrice) {
      flights = flights.filter(f => f.pricePerSeatUSD <= parseInt(maxPrice));
    }

    return flights.sort((a, b) => {
      switch (sortBy) {
        case 'cheapest': return a.pricePerSeatUSD - b.pricePerSeatUSD;
        case 'fastest': 
          return (new Date(a.arrivalTime).getTime() - new Date(a.departureTime).getTime()) - (new Date(b.arrivalTime).getTime() - new Date(b.departureTime).getTime());
        case 'mostSeats': return b.availableSeats - a.availableSeats;
        case 'pilotRating': return b.pilotRating - a.pilotRating;
        default: return 0;
      }
    });
  }, [departure, arrival, date, passengers, petFriendly, maxPrice, sortBy]);

  return (
    <div className="space-y-10"> {/* Compacted space-y-12 */}
      <h1 className={`text-3xl font-bold text-center ${THEME_COLORS.gradientTextUser} mb-6`}>Discover Your Flight Vector</h1> {/* Compacted text-4xl, mb-8 */}
      
      <section className={`${THEME_COLORS.bgGlass} p-5 md:p-6 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/20 scroll-animation`}> {/* Compacted p-6/8 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4"> {/* Compacted gap-6, mb-6 */}
          <InputField label="Departure Matrix (ICAO)" placeholder="e.g., KTEB" value={departure} onChange={e => setDeparture(e.target.value)} icon={<MapPinIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} /> {/* Compacted icon size */}
          <InputField label="Arrival Nexus (ICAO)" placeholder="e.g., KVNY" value={arrival} onChange={e => setArrival(e.target.value)} icon={<MapPinIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
          <InputField label="Target Date" type="date" placeholder="Select Date" value={date} onChange={e => setDate(e.target.value)} icon={<CalendarDaysIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
          <InputField label="Passengers" type="number" placeholder="e.g., 1" value={passengers} onChange={e => setPassengers(e.target.value)} icon={<UserGroupIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
          <InputField label="Max Price (USD)" type="number" placeholder="e.g., 5000" value={maxPrice} onChange={e => setMaxPrice(e.target.value)} icon={<CurrencyDollarIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
          
          <div className="flex-1 min-w-[180px] flex items-end"> {/* Compacted min-w */}
             <label htmlFor="petFriendly" className={`flex items-center cursor-pointer ${THEME_COLORS.bgGlass} p-2.5 rounded-lg border ${THEME_COLORS.borderPrimaryAccent}/50 w-full h-[46px]`}> {/* Compacted p-3, h-[50px] */}
              <input 
                type="checkbox" 
                id="petFriendly" 
                checked={petFriendly} 
                onChange={e => setPetFriendly(e.target.checked)} 
                className="form-checkbox-custom mr-2" // Compacted mr-3
              />
              <span className={`${THEME_COLORS.textPrimaryAccent} font-medium text-xs`}>Pet Friendly</span> {/* Compacted text size */}
              <PetIcon className={`w-4 h-4 ${THEME_COLORS.textMedium} ml-auto`}/> {/* Compacted icon size */}
            </label>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className="relative mb-3 sm:mb-0 w-full sm:w-auto"> {/* Compacted mb-4 */}
                <label htmlFor="sortBy" className={`block text-xs font-medium ${THEME_COLORS.textPrimaryAccent} mb-0.5`}>Sort by</label> {/* Compacted text-sm, mb-1 */}
                <select
                id="sortBy"
                value={sortBy}
                onChange={e => setSortBy(e.target.value as any)}
                className={`appearance-none w-full sm:w-auto p-2.5 pr-7 text-sm ${THEME_COLORS.bgGlass} rounded-lg border ${THEME_COLORS.borderPrimaryAccent}/50 focus:ring-2 ${THEME_COLORS.ringPrimaryAccent} focus:${THEME_COLORS.borderPrimaryAccent} outline-none transition-all ${THEME_COLORS.textLight} placeholder-[var(--color-text-dark)] bg-transparent`} // Compacted p-3, pr-8
                >
                <option value="cheapest">Price: Ascending</option>
                <option value="fastest">Duration: Shortest</option>
                <option value="mostSeats">Seats: Descending</option>
                <option value="pilotRating">Rating: Descending</option>
                </select>
                <ChevronDownIcon className={`w-4 h-4 ${THEME_COLORS.textMedium} absolute right-2.5 top-[calc(50%+4px)] transform -translate-y-1/2 pointer-events-none`} /> {/* Compacted icon size, positioning */}
            </div>
        </div>
      </section>

      <section className={`${THEME_COLORS.bgGlass} p-4 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/10 h-56 md:h-80 flex items-center justify-center scroll-animation relative overflow-hidden`}> {/* Compacted p-6, h-64/96 */}
        <p className={`${THEME_COLORS.textPrimaryAccent} text-base z-10`}>Interactive Flight Trajectory Map (Initializing...)</p> {/* Compacted text-lg */}
        <img src="https://picsum.photos/seed/jetmapdarkpurple/800/400?blur=4" alt="Dark Purple Map placeholder" className="absolute inset-0 w-full h-full object-cover opacity-5 rounded-xl"/> {/* Reduced opacity */}
      </section>
      
      <section>
        <h2 className={`text-xl font-bold mb-5 ${THEME_COLORS.textSecondaryAccent}`}> {/* Compacted text-2xl, mb-6 */}
          {filteredFlights.length} Flight Vectors Identified
        </h2>
        {filteredFlights.length > 0 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"> {/* Compacted gap-8 */}
            {filteredFlights.map(flight => (
              <FlightCard key={flight.id} flight={flight} />
            ))}
          </div>
        ) : (
          <div className={`text-center py-10 ${THEME_COLORS.bgGlass} rounded-xl`}> {/* Compacted py-12 */}
            <p className={`text-lg ${THEME_COLORS.textMedium}`}>No flight vectors match your parameters.</p> {/* Compacted text-xl */}
            <p className={`${THEME_COLORS.textPrimaryAccent} mt-1.5 text-sm`}>Adjust search or await new listings in the data stream.</p> {/* Compacted mt-2 */}
          </div>
        )}
      </section>
    </div>
  );
};

export default FlightSearchPage;
