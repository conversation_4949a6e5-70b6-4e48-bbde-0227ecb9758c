import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface AnimatedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'ghost' | 'holographic';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  glowEffect?: boolean;
}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  glowEffect = true,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const baseClasses = `
    relative overflow-hidden font-semibold transition-all duration-300 ease-out
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500
    disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : 'inline-flex'}
    items-center justify-center gap-2
  `;

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm rounded-lg',
    md: 'px-4 py-2 text-base rounded-lg',
    lg: 'px-6 py-3 text-lg rounded-xl',
    xl: 'px-8 py-4 text-xl rounded-xl',
  };

  const variantClasses = {
    primary: `
      bg-gradient-to-r from-red-500 via-pink-500 to-purple-600
      text-white border border-purple-500/30
      hover:from-red-400 hover:via-pink-400 hover:to-purple-500
      ${glowEffect ? 'hover:shadow-lg hover:shadow-purple-500/25' : ''}
    `,
    secondary: `
      bg-gradient-to-r from-purple-600/20 to-red-500/20
      text-purple-300 border border-purple-500/50
      hover:border-purple-400 hover:text-purple-200
      backdrop-blur-sm
      ${glowEffect ? 'hover:shadow-lg hover:shadow-purple-500/20' : ''}
    `,
    ghost: `
      bg-transparent text-purple-300 border border-purple-500/30
      hover:bg-purple-500/10 hover:border-purple-400
      ${glowEffect ? 'hover:shadow-md hover:shadow-purple-500/15' : ''}
    `,
    holographic: `
      bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500
      text-white border-0
      hover:from-cyan-300 hover:via-purple-400 hover:to-pink-400
      ${glowEffect ? 'hover:shadow-xl hover:shadow-cyan-500/30' : ''}
    `,
  };

  const buttonVariants = {
    initial: { scale: 1 },
    hover: { 
      scale: 1.05,
      transition: { type: 'spring', stiffness: 400, damping: 10 }
    },
    tap: { 
      scale: 0.95,
      transition: { type: 'spring', stiffness: 400, damping: 10 }
    },
  };

  const shimmerVariants = {
    initial: { x: '-100%' },
    hover: { 
      x: '100%',
      transition: { duration: 0.6, ease: 'easeInOut' }
    },
  };

  const iconVariants = {
    initial: { rotate: 0 },
    hover: { 
      rotate: variant === 'holographic' ? 360 : 0,
      transition: { duration: 0.6, ease: 'easeInOut' }
    },
  };

  const pulseVariants = {
    initial: { scale: 1, opacity: 0.7 },
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 0.3, 0.7],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  const LoadingSpinner = () => (
    <motion.div
      className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    />
  );

  return (
    <motion.button
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
      variants={buttonVariants}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onTapStart={() => setIsPressed(true)}
      onTapEnd={() => setIsPressed(false)}
      onClick={onClick}
      disabled={disabled || loading}
    >
      {/* Background pulse effect for holographic variant */}
      {variant === 'holographic' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 rounded-inherit"
          variants={pulseVariants}
          initial="initial"
          animate="animate"
        />
      )}

      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        variants={shimmerVariants}
        initial="initial"
        animate={isHovered ? 'hover' : 'initial'}
      />

      {/* Content */}
      <div className="relative z-10 flex items-center gap-2">
        {loading ? (
          <LoadingSpinner />
        ) : (
          <>
            {icon && iconPosition === 'left' && (
              <motion.div
                variants={iconVariants}
                initial="initial"
                animate={isHovered ? 'hover' : 'initial'}
              >
                {icon}
              </motion.div>
            )}
            
            <motion.span
              initial={{ opacity: 1 }}
              animate={{ 
                opacity: loading ? 0 : 1,
                transition: { duration: 0.2 }
              }}
            >
              {children}
            </motion.span>
            
            {icon && iconPosition === 'right' && (
              <motion.div
                variants={iconVariants}
                initial="initial"
                animate={isHovered ? 'hover' : 'initial'}
              >
                {icon}
              </motion.div>
            )}
          </>
        )}
      </div>

      {/* Glow effect */}
      {glowEffect && (
        <motion.div
          className="absolute inset-0 rounded-inherit opacity-0"
          style={{
            background: variant === 'holographic' 
              ? 'radial-gradient(circle, rgba(0,255,255,0.3) 0%, transparent 70%)'
              : 'radial-gradient(circle, rgba(157,0,255,0.3) 0%, transparent 70%)',
          }}
          animate={{
            opacity: isHovered ? 1 : 0,
            scale: isHovered ? 1.1 : 1,
          }}
          transition={{ duration: 0.3 }}
        />
      )}
    </motion.button>
  );
};

export default AnimatedButton;
