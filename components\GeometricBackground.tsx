import React, { useEffect, useRef } from 'react';

interface Plane {
  x: number;
  y: number;
  z: number;
  size: number;
  rotation: number;
  rotationSpeed: number;
  color: string;
  opacity: number;
  velocityX: number;
  velocityY: number;
  velocityZ: number;
  type: 'fighter' | 'commercial' | 'private';
}

const GeometricBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const planesRef = useRef<Plane[]>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize planes
    const initPlanes = () => {
      const planes: Plane[] = [];
      const planeCount = 60; // Number of planes

      const colors = [
        '#FF0080', // Magenta
        '#8000FF', // Purple
        '#0080FF', // Blue
        '#FF4080', // Pink
        '#4080FF', // Light Blue
        '#FF8040', // Orange
        '#8040FF', // Violet
        '#40FF80', // Green
      ];

      const planeTypes: ('fighter' | 'commercial' | 'private')[] = ['fighter', 'commercial', 'private'];

      for (let i = 0; i < planeCount; i++) {
        planes.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          z: Math.random() * 1000 + 100,
          size: Math.random() * 30 + 15,
          rotation: Math.random() * Math.PI * 2,
          rotationSpeed: (Math.random() - 0.5) * 0.03,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.8 + 0.2,
          velocityX: (Math.random() - 0.5) * 1.2,
          velocityY: (Math.random() - 0.5) * 0.8,
          velocityZ: (Math.random() - 0.5) * 2,
          type: planeTypes[Math.floor(Math.random() * planeTypes.length)],
        });
      }

      planesRef.current = planes;
    };

    // Draw different types of planes
    const drawPlane = (plane: Plane) => {
      const { x, y, z, size, rotation, color, opacity, type } = plane;

      // Simple 3D projection
      const scale = 300 / (300 + z);
      const projectedX = x * scale + canvas.width / 2;
      const projectedY = y * scale + canvas.height / 2;
      const projectedSize = size * scale;

      if (projectedSize < 2) return; // Don't draw very small planes

      ctx.save();
      ctx.translate(projectedX, projectedY);
      ctx.rotate(rotation);
      ctx.globalAlpha = opacity * scale;

      // Set colors
      ctx.fillStyle = color;
      ctx.strokeStyle = color;
      ctx.lineWidth = Math.max(1, projectedSize / 15);

      // Draw different plane types
      switch (type) {
        case 'fighter':
          drawFighterJet(projectedSize);
          break;
        case 'commercial':
          drawCommercialPlane(projectedSize);
          break;
        case 'private':
          drawPrivateJet(projectedSize);
          break;
      }

      // Add glow effect
      ctx.shadowColor = color;
      ctx.shadowBlur = projectedSize / 3;
      ctx.stroke();

      ctx.restore();
    };

    // Fighter jet shape
    const drawFighterJet = (size: number) => {
      ctx.beginPath();
      // Fuselage
      ctx.moveTo(-size/2, 0);
      ctx.lineTo(size/2, 0);
      // Wings
      ctx.moveTo(-size/4, 0);
      ctx.lineTo(-size/3, -size/3);
      ctx.moveTo(-size/4, 0);
      ctx.lineTo(-size/3, size/3);
      ctx.moveTo(size/4, 0);
      ctx.lineTo(size/3, -size/4);
      ctx.moveTo(size/4, 0);
      ctx.lineTo(size/3, size/4);
      // Tail
      ctx.moveTo(-size/2, 0);
      ctx.lineTo(-size/1.5, -size/6);
      ctx.moveTo(-size/2, 0);
      ctx.lineTo(-size/1.5, size/6);
    };

    // Commercial plane shape
    const drawCommercialPlane = (size: number) => {
      ctx.beginPath();
      // Fuselage
      ctx.ellipse(0, 0, size/2, size/8, 0, 0, Math.PI * 2);
      // Wings
      ctx.moveTo(-size/6, 0);
      ctx.lineTo(-size/3, -size/2.5);
      ctx.lineTo(size/6, -size/6);
      ctx.moveTo(-size/6, 0);
      ctx.lineTo(-size/3, size/2.5);
      ctx.lineTo(size/6, size/6);
      // Tail
      ctx.moveTo(-size/2.2, 0);
      ctx.lineTo(-size/1.8, -size/4);
      ctx.lineTo(-size/2.5, -size/5);
    };

    // Private jet shape
    const drawPrivateJet = (size: number) => {
      ctx.beginPath();
      // Fuselage
      ctx.moveTo(-size/2, 0);
      ctx.lineTo(size/2.5, 0);
      // Wings
      ctx.moveTo(-size/8, 0);
      ctx.lineTo(-size/4, -size/3);
      ctx.lineTo(size/8, -size/8);
      ctx.moveTo(-size/8, 0);
      ctx.lineTo(-size/4, size/3);
      ctx.lineTo(size/8, size/8);
      // Tail
      ctx.moveTo(-size/2.5, 0);
      ctx.lineTo(-size/2, -size/5);
      ctx.moveTo(-size/2.5, 0);
      ctx.lineTo(-size/2, size/5);
      // Nose
      ctx.moveTo(size/2.5, 0);
      ctx.lineTo(size/2, -size/12);
      ctx.lineTo(size/2, size/12);
      ctx.closePath();
    };

    initPlanes();

    // Animation loop
    const animate = () => {
      // Clear with dark gradient background
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
      gradient.addColorStop(0, '#0a0015');
      gradient.addColorStop(0.5, '#1a0030');
      gradient.addColorStop(1, '#0f0020');

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Update and draw planes
      planesRef.current.forEach((plane) => {
        // Update position
        plane.x += plane.velocityX;
        plane.y += plane.velocityY;
        plane.z += plane.velocityZ;

        // Update rotation
        plane.rotation += plane.rotationSpeed;

        // Wrap around edges
        if (plane.x < -canvas.width/2) plane.x = canvas.width/2;
        if (plane.x > canvas.width/2) plane.x = -canvas.width/2;
        if (plane.y < -canvas.height/2) plane.y = canvas.height/2;
        if (plane.y > canvas.height/2) plane.y = -canvas.height/2;
        if (plane.z < -500) plane.z = 1000;
        if (plane.z > 1000) plane.z = -500;

        // Pulse opacity
        plane.opacity = 0.3 + 0.4 * Math.sin(Date.now() * 0.001 + plane.x * 0.01);

        drawPlane(plane);
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 w-full h-full pointer-events-none"
      style={{ 
        zIndex: -1,
        background: 'linear-gradient(135deg, #0a0015 0%, #1a0030 50%, #0f0020 100%)'
      }}
    />
  );
};

export default GeometricBackground;
