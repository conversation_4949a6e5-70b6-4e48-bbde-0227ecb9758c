import React, { useEffect, useRef } from 'react';

interface Cube {
  x: number;
  y: number;
  z: number;
  size: number;
  rotationX: number;
  rotationY: number;
  rotationZ: number;
  rotationSpeedX: number;
  rotationSpeedY: number;
  rotationSpeedZ: number;
  color: string;
  opacity: number;
  velocityX: number;
  velocityY: number;
  velocityZ: number;
}

const GeometricBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const cubesRef = useRef<Cube[]>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize cubes
    const initCubes = () => {
      const cubes: Cube[] = [];
      const cubeCount = 80; // Number of cubes
      
      const colors = [
        '#FF0080', // Magenta
        '#8000FF', // Purple
        '#0080FF', // Blue
        '#FF4080', // Pink
        '#4080FF', // Light Blue
        '#FF8040', // Orange
        '#8040FF', // Violet
        '#40FF80', // Green
      ];

      for (let i = 0; i < cubeCount; i++) {
        cubes.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          z: Math.random() * 1000 + 100,
          size: Math.random() * 40 + 20,
          rotationX: Math.random() * Math.PI * 2,
          rotationY: Math.random() * Math.PI * 2,
          rotationZ: Math.random() * Math.PI * 2,
          rotationSpeedX: (Math.random() - 0.5) * 0.02,
          rotationSpeedY: (Math.random() - 0.5) * 0.02,
          rotationSpeedZ: (Math.random() - 0.5) * 0.02,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.8 + 0.2,
          velocityX: (Math.random() - 0.5) * 0.5,
          velocityY: (Math.random() - 0.5) * 0.5,
          velocityZ: (Math.random() - 0.5) * 2,
        });
      }
      
      cubesRef.current = cubes;
    };

    // Draw a 3D cube
    const drawCube = (cube: Cube) => {
      const { x, y, z, size, rotationX, rotationY, rotationZ, color, opacity } = cube;
      
      // Simple 3D projection
      const scale = 300 / (300 + z);
      const projectedX = x * scale + canvas.width / 2;
      const projectedY = y * scale + canvas.height / 2;
      const projectedSize = size * scale;

      if (projectedSize < 1) return; // Don't draw very small cubes

      ctx.save();
      ctx.translate(projectedX, projectedY);
      ctx.globalAlpha = opacity * scale;

      // Create gradient for 3D effect
      const gradient = ctx.createLinearGradient(-projectedSize/2, -projectedSize/2, projectedSize/2, projectedSize/2);
      gradient.addColorStop(0, color);
      gradient.addColorStop(0.5, color + '80');
      gradient.addColorStop(1, color + '40');

      // Draw cube faces with rotation effect
      const cos = Math.cos(rotationY);
      const sin = Math.sin(rotationY);
      
      // Front face
      ctx.fillStyle = gradient;
      ctx.fillRect(-projectedSize/2, -projectedSize/2, projectedSize, projectedSize);
      
      // Right face (darker)
      ctx.fillStyle = color + '60';
      ctx.beginPath();
      ctx.moveTo(projectedSize/2, -projectedSize/2);
      ctx.lineTo(projectedSize/2 + projectedSize/4, -projectedSize/2 - projectedSize/4);
      ctx.lineTo(projectedSize/2 + projectedSize/4, projectedSize/2 - projectedSize/4);
      ctx.lineTo(projectedSize/2, projectedSize/2);
      ctx.closePath();
      ctx.fill();
      
      // Top face (lighter)
      ctx.fillStyle = color + '80';
      ctx.beginPath();
      ctx.moveTo(-projectedSize/2, -projectedSize/2);
      ctx.lineTo(-projectedSize/2 + projectedSize/4, -projectedSize/2 - projectedSize/4);
      ctx.lineTo(projectedSize/2 + projectedSize/4, -projectedSize/2 - projectedSize/4);
      ctx.lineTo(projectedSize/2, -projectedSize/2);
      ctx.closePath();
      ctx.fill();

      // Add glow effect
      ctx.shadowColor = color;
      ctx.shadowBlur = projectedSize / 4;
      ctx.strokeStyle = color;
      ctx.lineWidth = 1;
      ctx.strokeRect(-projectedSize/2, -projectedSize/2, projectedSize, projectedSize);

      ctx.restore();
    };

    initCubes();

    // Animation loop
    const animate = () => {
      // Clear with dark gradient background
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
      gradient.addColorStop(0, '#0a0015');
      gradient.addColorStop(0.5, '#1a0030');
      gradient.addColorStop(1, '#0f0020');
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Update and draw cubes
      cubesRef.current.forEach((cube) => {
        // Update position
        cube.x += cube.velocityX;
        cube.y += cube.velocityY;
        cube.z += cube.velocityZ;

        // Update rotation
        cube.rotationX += cube.rotationSpeedX;
        cube.rotationY += cube.rotationSpeedY;
        cube.rotationZ += cube.rotationSpeedZ;

        // Wrap around edges
        if (cube.x < -canvas.width/2) cube.x = canvas.width/2;
        if (cube.x > canvas.width/2) cube.x = -canvas.width/2;
        if (cube.y < -canvas.height/2) cube.y = canvas.height/2;
        if (cube.y > canvas.height/2) cube.y = -canvas.height/2;
        if (cube.z < -500) cube.z = 1000;
        if (cube.z > 1000) cube.z = -500;

        // Pulse opacity
        cube.opacity = 0.3 + 0.4 * Math.sin(Date.now() * 0.001 + cube.x * 0.01);

        drawCube(cube);
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 w-full h-full pointer-events-none"
      style={{ 
        zIndex: -1,
        background: 'linear-gradient(135deg, #0a0015 0%, #1a0030 50%, #0f0020 100%)'
      }}
    />
  );
};

export default GeometricBackground;
