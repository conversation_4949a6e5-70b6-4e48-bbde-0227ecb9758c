import React,
 { createContext, useState, useContext, ReactNode, useCallback } from 'react';
import { AuthState, AuthContextType, UserProfile } from '../types';
import { MOCK_USERS } from '../constants';

const defaultAuthState: AuthState = {
  isAuthenticated: false,
  currentUser: null,
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>(defaultAuthState);

  const login = useCallback(async (email: string, password?: string): Promise<UserProfile | null> => {
    // Mock API call & password check
    // In a real app, password would be checked against a hash.
    // For this mock, we'll ignore the password and find by email.
    console.log(`Attempting login for email: ${email}`);
    const foundUser = MOCK_USERS.find(user => user.email?.toLowerCase() === email.toLowerCase());

    if (foundUser) {
      console.log('User found:', foundUser);
      setAuthState({
        isAuthenticated: true,
        currentUser: foundUser,
      });
      return foundUser;
    }
    console.log('User not found for email:', email);
    setAuthState(defaultAuthState); // Ensure state is reset on failed login
    return null;
  }, []);

  const logout = useCallback(() => {
    setAuthState(defaultAuthState);
  }, []);

  return (
    <AuthContext.Provider value={{ ...authState, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};