import React, { useRef, useEffect, useMemo } from 'react';
import * as THREE from 'three';

interface Enhanced3DHeroProps {
  className?: string;
}

const Enhanced3DHero: React.FC<Enhanced3DHeroProps> = ({ className = '' }) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const frameIdRef = useRef<number>();

  // Create aircraft geometry
  const createAircraft = useMemo(() => {
    const aircraft = new THREE.Group();
    
    // Fuselage
    const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.1, 4, 8);
    const fuselageMaterial = new THREE.MeshPhongMaterial({
      color: 0x9D00FF,
      transparent: true,
      opacity: 0.8,
      emissive: 0x330066,
    });
    const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
    fuselage.rotation.z = Math.PI / 2;
    aircraft.add(fuselage);

    // Wings
    const wingGeometry = new THREE.BoxGeometry(3, 0.1, 0.8);
    const wingMaterial = new THREE.MeshPhongMaterial({
      color: 0xFF003C,
      transparent: true,
      opacity: 0.7,
      emissive: 0x660011,
    });
    const wings = new THREE.Mesh(wingGeometry, wingMaterial);
    wings.position.z = -0.5;
    aircraft.add(wings);

    // Tail
    const tailGeometry = new THREE.BoxGeometry(0.8, 1.2, 0.1);
    const tailMaterial = new THREE.MeshPhongMaterial({
      color: 0xF02D7A,
      transparent: true,
      opacity: 0.6,
      emissive: 0x440022,
    });
    const tail = new THREE.Mesh(tailGeometry, tailMaterial);
    tail.position.x = -1.8;
    aircraft.add(tail);

    return aircraft;
  }, []);

  // Create particle system
  const createParticleSystem = useMemo(() => {
    const particleCount = 1000;
    const particles = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);

    const colorPalette = [
      new THREE.Color(0x9D00FF), // Purple
      new THREE.Color(0xFF003C), // Red
      new THREE.Color(0xF02D7A), // Pink
      new THREE.Color(0x00FFFF), // Cyan
    ];

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      
      // Positions
      positions[i3] = (Math.random() - 0.5) * 50;
      positions[i3 + 1] = (Math.random() - 0.5) * 50;
      positions[i3 + 2] = (Math.random() - 0.5) * 50;

      // Colors
      const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];
      colors[i3] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;

      // Sizes
      sizes[i] = Math.random() * 3 + 1;
    }

    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    const particleMaterial = new THREE.PointsMaterial({
      size: 2,
      transparent: true,
      opacity: 0.6,
      vertexColors: true,
      blending: THREE.AdditiveBlending,
    });

    return new THREE.Points(particles, particleMaterial);
  }, []);

  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    scene.fog = new THREE.Fog(0x08030D, 10, 50);
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 0, 10);
    cameraRef.current = camera;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true, 
      alpha: true,
      powerPreference: 'high-performance'
    });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.setClearColor(0x08030D, 0.1);
    rendererRef.current = renderer;

    mountRef.current.appendChild(renderer.domElement);

    // Lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0x9D00FF, 1);
    directionalLight.position.set(5, 5, 5);
    scene.add(directionalLight);

    const pointLight = new THREE.PointLight(0xFF003C, 1, 100);
    pointLight.position.set(-5, -5, 5);
    scene.add(pointLight);

    // Add aircraft
    const aircraft1 = createAircraft.clone();
    aircraft1.position.set(-8, 2, -5);
    aircraft1.rotation.y = Math.PI / 4;
    scene.add(aircraft1);

    const aircraft2 = createAircraft.clone();
    aircraft2.position.set(8, -2, -8);
    aircraft2.rotation.y = -Math.PI / 3;
    aircraft2.scale.setScalar(0.7);
    scene.add(aircraft2);

    const aircraft3 = createAircraft.clone();
    aircraft3.position.set(0, 5, -12);
    aircraft3.rotation.y = Math.PI / 6;
    aircraft3.scale.setScalar(0.5);
    scene.add(aircraft3);

    // Add particle system
    scene.add(createParticleSystem);

    // Animation loop
    const animate = () => {
      frameIdRef.current = requestAnimationFrame(animate);

      const time = Date.now() * 0.001;

      // Rotate aircraft
      aircraft1.rotation.y += 0.005;
      aircraft1.position.y = 2 + Math.sin(time * 0.5) * 0.5;

      aircraft2.rotation.y -= 0.003;
      aircraft2.position.y = -2 + Math.cos(time * 0.7) * 0.3;

      aircraft3.rotation.y += 0.007;
      aircraft3.position.y = 5 + Math.sin(time * 0.3) * 0.8;

      // Animate particles
      const positions = createParticleSystem.geometry.attributes.position.array as Float32Array;
      for (let i = 0; i < positions.length; i += 3) {
        positions[i + 1] += Math.sin(time + positions[i]) * 0.01;
      }
      createParticleSystem.geometry.attributes.position.needsUpdate = true;

      // Camera movement
      camera.position.x = Math.sin(time * 0.1) * 2;
      camera.position.y = Math.cos(time * 0.15) * 1;
      camera.lookAt(0, 0, 0);

      renderer.render(scene, camera);
    };

    animate();

    // Handle resize
    const handleResize = () => {
      if (!mountRef.current) return;
      
      camera.aspect = mountRef.current.clientWidth / mountRef.current.clientHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      if (frameIdRef.current) {
        cancelAnimationFrame(frameIdRef.current);
      }
      window.removeEventListener('resize', handleResize);
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    };
  }, [createAircraft, createParticleSystem]);

  return (
    <div 
      ref={mountRef} 
      className={`absolute inset-0 ${className}`}
      style={{ 
        background: 'linear-gradient(135deg, rgba(8, 3, 13, 0.9) 0%, rgba(10, 5, 16, 0.8) 50%, rgba(13, 8, 32, 0.7) 100%)'
      }}
    />
  );
};

export default Enhanced3DHero;
