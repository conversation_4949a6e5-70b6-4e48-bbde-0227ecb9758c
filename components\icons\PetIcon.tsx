
import React from 'react';

// Simple paw print icon for pets
export const PetIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm2.71 13.29c-.18.18-.44.29-.71.29s-.53-.11-.71-.29l-1.29-1.29c-.18-.18-.29-.44-.29-.71s.11-.53.29-.71c.38-.38 1.04-.38 1.42 0l.58.58.58-.58c.38-.38 1.04-.38 1.42 0 .38.38.38 1.04 0 1.42l-1.29 1.29zm-4.5-3.58c-.18.18-.44.29-.71.29s-.53-.11-.71-.29c-.38-.38-.38-1.04 0-1.42l1.29-1.29c.18-.18.44-.29.71-.29s.53.11.71.29c.38.38.38 1.04 0 1.42l-1.29 1.29zm5 0c-.18.18-.44.29-.71.29s-.53-.11-.71-.29c-.38-.38-.38-1.04 0-1.42l1.29-1.29c.18-.18.44-.29.71-.29s.53.11.71.29c.38.38.38 1.04 0 1.42l-1.29 1.29zm-2.5-5C11.53 7.71 11 7.82 11 8.09V9.5c0 .28.22.5.5.5s.5-.22.5-.5V8.09c0-.27-.53-.38-1.21-.09zM10 9.71c-.18.18-.44.29-.71.29s-.53-.11-.71-.29c-.38-.38-.38-1.04 0-1.42l.29-.29c.18-.18.44-.29.71-.29s.53.11.71.29l.29.29c.38.38.38 1.04 0 1.42z" />
  </svg>
);
