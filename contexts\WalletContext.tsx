
import React, { createContext, useState, useContext, ReactNode } from 'react';
import { WalletState, WalletContextType } from '../types';

const defaultState: WalletState = {
  isConnected: false,
  address: null,
  balance: null,
};

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export const WalletProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [walletState, setWalletState] = useState<WalletState>(defaultState);

  const connectWallet = () => {
    // Simulate wallet connection
    setWalletState({
      isConnected: true,
      address: `0x${Math.random().toString(16).substr(2, 8).toUpperCase()}...${Math.random().toString(16).substr(2, 8).toUpperCase()}`,
      balance: { eth: parseFloat((Math.random() * 10).toFixed(2)), btc: parseFloat((Math.random() * 0.5).toFixed(4)) },
    });
  };

  const disconnectWallet = () => {
    setWalletState(defaultState);
  };

  return (
    <WalletContext.Provider value={{ ...walletState, connectWallet, disconnectWallet }}>
      {children}
    </WalletContext.Provider>
  );
};

export const useWallet = (): WalletContextType => {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};
