
import { Flight, UserProfile, UserRole, BadgeType, MembershipTier } from './types';

export const ROUTES = {
  HOME: '/',
  SEARCH_FLIGHTS: '/search-flights',
  USER_PROFILE: '/profile', // append /:userId
  LIST_LEG: '/list-leg',
  MEMBERSHIP: '/membership',
  MEMBERS_DIRECTORY: '/members',
  SAFETY: '/safety',
  PET_TRAVEL: '/pet-travel',
  TERMS: '/terms',
  PRIVACY: '/privacy',
  FAA_DISCLAIMER: '/faa-disclaimer',
  CRYPTO_PAYMENTS: '/crypto-terms',
  LOGIN: '/login',
  PILOT_DASHBOARD: '/dashboard/pilot',
  TRAVELER_DASHBOARD: '/dashboard/traveler',
};

export const THEME_COLORS = {
  // Accents (using CSS variables defined in index.html)
  textPrimaryAccent: 'text-[var(--color-primary-purple)]', // Purple
  textSecondaryAccent: 'text-[var(--color-secondary-red)]', // Red
  borderPrimaryAccent: 'border-[var(--color-primary-purple)]',
  borderSecondaryAccent: 'border-[var(--color-secondary-red)]',
  ringPrimaryAccent: 'ring-[var(--color-primary-purple)]',
  ringSecondaryAccent: 'ring-[var(--color-secondary-red)]', // Added for consistency
  
  // Gradients (classes defined in index.html)
  gradientTextUser: 'text-gradient-theme-user', // Purple -> Pink -> Red
  gradientTextSubtle: 'text-gradient-theme-subtle', // Medium Grey -> Light Grey
  gradientBgButton: 'bg-gradient-theme-button', // Red -> Pink -> Purple

  // General Text (using CSS variables)
  textLight: 'text-[var(--color-text-light)]',
  textMedium: 'text-[var(--color-text-medium)]',
  textDark: 'text-[var(--color-text-dark)]',

  // Backgrounds & Effects
  bgDark: 'bg-[var(--color-dark-bg)]', // Main dark background
  bgGlass: 'glassmorphic', // Glassmorphic effect class
  
  // Glows & Shadows (classes defined in index.html or Tailwind extended)
  shadowPrimary: 'shadow-theme-primary-glow', // Purple glow
  shadowSecondary: 'shadow-theme-secondary-glow', // Red glow
  buttonGlow: 'button-glow-theme', // General button glow for primary actions
  buttonGlowSecondary: 'button-glow-theme-secondary', // Glow for secondary actions
};


export const MOCK_FLIGHTS: Flight[] = [
  {
    id: 'fl001',
    pilotId: 'p001',
    pilotName: 'Capt. Alex "Vector" Chen',
    pilotRating: 4.9,
    aircraftType: 'Gulfstream G700 (Void Runner)',
    departureAirport: 'KTEB (Teterboro)', 
    arrivalAirport: 'KVNY (Van Nuys)', 
    departureTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    arrivalTime: new Date(Date.now() + 2.25 * 24 * 60 * 60 * 1000).toISOString(),
    availableSeats: 4,
    pricePerSeatUSD: 3100,
    pricePerSeatETH: 0.9,
    isPetFriendly: true,
    description: 'Ultra-luxe transcontinental flight. On-chain verified. Quantum-encrypted comms. Red/Purple theme amenities.',
    verifiedPilot: true,
  },
  {
    id: 'fl002',
    pilotId: 'p002',
    pilotName: 'Sofia "Nyx" Ramirez',
    pilotRating: 4.7,
    aircraftType: 'Bombardier Global 8000 (Crimson Phantom)',
    departureAirport: 'EGGW (London Luton)', 
    arrivalAirport: 'LEMD (Madrid Barajas)', 
    departureTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
    arrivalTime: new Date(Date.now() + 3.1 * 24 * 60 * 60 * 1000).toISOString(),
    availableSeats: 2,
    pricePerSeatUSD: 2250,
    pricePerSeatETH: 0.65,
    isPetFriendly: false,
    verifiedPilot: true,
    description: 'Stealthy & swift. Ideal for high-privacy journeys. ETH preferred for seat acquisition.'
  },
  {
    id: 'fl003',
    pilotId: 'p003',
    pilotName: 'Kenji "Ronin" Tanaka',
    pilotRating: 4.8,
    aircraftType: 'Cessna Citation X+ (Cyberpunk Custom)',
    departureAirport: 'OMDB (Dubai Intl)', 
    arrivalAirport: 'VABB (Mumbai Intl)', 
    departureTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
    arrivalTime: new Date(Date.now() + 5.12 * 24 * 60 * 60 * 1000).toISOString(),
    availableSeats: 6,
    pricePerSeatUSD: 1500,
    isPetFriendly: true,
    description: 'Fast, agile, and pet-friendly. Experience the neon-drenched skies in style.',
    verifiedPilot: false,
  },
];

export const MOCK_USERS: UserProfile[] = [
  {
    id: 'p001',
    name: 'Capt. Alex "Vector" Chen',
    email: '<EMAIL>',
    role: UserRole.Pilot,
    walletAddress: '0xVECTOR...C789',
    profilePictureUrl: 'https://picsum.photos/seed/pilotRedPurp1/200/200',
    location: 'Neo-New York, Sector 7',
    memberSince: new Date(Date.now() - 300 * 24 * 60 * 60 * 1000).toISOString(),
    bio: 'Pioneering Web4 flight dynamics. Safety, Speed, Sovereignty. Specializes in Quantum Entangled Routes.',
    badges: [BadgeType.VerifiedPilot, BadgeType.CryptoNative, BadgeType.TopRated, BadgeType.PetFriendlyHost],
    aircraftDetails: [{ make: 'Gulfstream', model: 'G700 (Void Runner)', registration: 'N777JSV' }],
    certifications: ['Galactic Transport Pilot', 'Type Rated G700-VR', 'Zero-G Flight Certified'],
    pilotRating: 4.9,
  },
  {
    id: 'p002',
    name: 'Sofia "Nyx" Ramirez',
    email: '<EMAIL>',
    role: UserRole.Pilot,
    walletAddress: '0xNYX...R456',
    profilePictureUrl: 'https://picsum.photos/seed/pilotRedPurp2/200/200',
    location: 'Aethelburg, UK Sprawl',
    memberSince: new Date(Date.now() - 150 * 24 * 60 * 60 * 1000).toISOString(),
    bio: 'Master of discrete aviation & secure comms. If it needs to be off-grid, I fly it.',
    badges: [BadgeType.VerifiedPilot, BadgeType.CryptoNative],
    aircraftDetails: [{ make: 'Bombardier', model: 'Global 8000 (Crimson Phantom)', registration: 'G-NYX0' }],
    certifications: ['EASA ATPL (Encrypted)', 'Type Rated Global 8000-CP', 'Stealth Flight Ops'],
    pilotRating: 4.7,
  },
  {
    id: 'p003',
    name: 'Kenji "Ronin" Tanaka',
    email: '<EMAIL>',
    role: UserRole.Pilot,
    profilePictureUrl: 'https://picsum.photos/seed/pilotRedPurp3/200/200',
    location: 'Neo-Kyoto, Sector Alpha',
    memberSince: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
    bio: 'Code, Fly, Repeat. Building the future of aviation, one smart contract at a time.',
    badges: [BadgeType.CryptoNative, BadgeType.PetFriendlyHost],
    aircraftDetails: [{ make: 'Cessna', model: 'Citation X+ (Cyberpunk Custom)', registration: 'JA-R0NIN' }],
    certifications: ['JCAB ATPL', 'Type Rated Citation X+', 'DAO Aviation Governance'],
    pilotRating: 4.8,
  },
  {
    id: 't001',
    name: 'Elena "Glitch" Ivanova',
    email: '<EMAIL>',
    role: UserRole.Traveler,
    walletAddress: '0xGLITCH...I123',
    profilePictureUrl: 'https://picsum.photos/seed/travelerRedPurp1/200/200',
    location: 'Singapore Sub-Orbital Arcology',
    memberSince: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    bio: 'Digital nomad surfing the waves of Web5. Always seeking the next red-eye to a new reality.',
    badges: [BadgeType.CryptoNative, BadgeType.EarlyAdopter],
    preferences: ['Pet-friendly (cybernetic companions included)', 'Sub-orbital hops preferred', 'Dark Fiber Wi-Fi'],
    pilotRating: 0, 
  },
];

export const MEMBERSHIP_TIERS: MembershipTier[] = [
  {
    id: 'ascendant',
    name: 'Ascendant Tier',
    priceUSD: 'Free',
    features: ['Standard flight matrix access', 'Basic profile', 'Community datastream access'],
    glowColor: 'gray', // Or a very subtle purple/red
  },
  {
    id: 'etherion',
    name: 'Etherion Elite',
    priceUSD: 69,
    priceCrypto: { amount: 0.02, currency: 'ETH' },
    features: ['Priority matrix filters', 'Early access to new flight drops', 'Enhanced profile customization', 'Basic flight path analytics'],
    isPopular: true,
    glowColor: 'purple', // Maps to primary accent
  },
  {
    id: 'singularity',
    name: 'Singularity Syndicate',
    priceUSD: 299,
    priceCrypto: { amount: 0.085, currency: 'ETH' },
    features: ['All Etherion perks', 'Access to off-grid/dark flights', 'Encrypted comms channel with support', 'Exclusive NFT Aviator Badge', 'Governance voting rights (future)'],
    glowColor: 'red', // Maps to secondary accent or a gradient mix
  },
];

// For Header/Footer Navigation
export const NAV_LINKS_CONFIG = {
    findFlights: "Find Flights",
    listLeg: "List a Leg",
    membership: "Membership",
    directory: "Directory",
    dashboard: "Dashboard",
    login: "Login / Sign Up",
    connectWallet: "Sync Wallet" // Updated for theme
};