
import React from 'react';
import { Link } from 'react-router-dom';
import { THEME_COLORS, ROUTES } from '../constants';
import { PetIcon } from '../components/icons/PetIcon'; 

const PetBenefit: React.FC<{ title: string; description: string; iconColor: string }> = ({ title, description, iconColor }) => (
    <div className={`${THEME_COLORS.bgGlass} p-5 rounded-xl shadow-lg hover:${THEME_COLORS.shadowPrimary}/30 transition-shadow duration-300 flex items-start space-x-3 scroll-animation`}> {/* Compacted p-6, space-x-4 */}
        <PetIcon className={`w-8 h-8 flex-shrink-0 mt-0.5 ${iconColor}`} /> {/* Compacted w-10 h-10, mt-1 */}
        <div>
            <h3 className={`text-xl font-semibold mb-1 ${THEME_COLORS.gradientTextUser}`}>{title}</h3> {/* Compacted text-2xl */}
            <p className={`${THEME_COLORS.textMedium} text-xs`}>{description}</p> {/* Compacted text size */}
        </div>
    </div>
);

const PetTravelPage: React.FC = () => {
  return (
    <div className="max-w-3xl mx-auto space-y-10"> {/* Compacted max-w-4xl, space-y-12 */}
      <section className="text-center scroll-animation">
        <PetIcon className={`w-20 h-20 mx-auto mb-5 ${THEME_COLORS.textSecondaryAccent}`} /> {/* Compacted w-24 h-24, mb-6 */}
        <h1 className={`text-3xl md:text-4xl font-bold ${THEME_COLORS.gradientTextUser} mb-3`}>Cyber-Companion Flights on JetSharing.co</h1> {/* Compacted text-4xl/5xl, mb-4 */}
        <p className={`text-base md:text-lg ${THEME_COLORS.textMedium} max-w-xl mx-auto`}> {/* Compacted max-w-2xl */}
          At JetSharing.co, we recognize that companions, biological or cybernetic, are integral. Many pilots on our platform welcome your co-pilots onboard, offering a seamless travel experience.
        </p>
      </section>

      <section className="space-y-5"> {/* Compacted space-y-6 */}
        <PetBenefit 
          title="In-Cabin Integration"
          description="Forget archaic cargo protocols. On companion-friendly flights, your entities can often integrate directly into the cabin environment, ensuring their operational comfort."
          iconColor={THEME_COLORS.textSecondaryAccent}
        />
        <PetBenefit 
          title="Adaptive Protocols"
          description="Individual pilots define their companion protocols; many are adaptable to diverse entity types. Always verify parameters directly with the pilot AI or human."
          iconColor={THEME_COLORS.textSecondaryAccent}
        />
        <PetBenefit 
          title="Reduced Transit Disruption"
          description="Private flight vectors significantly reduce companion anxiety compared to commercial airline procedures. Enjoy a smoother shared journey through the skies."
          iconColor={THEME_COLORS.textPrimaryAccent} 
        />
         <PetBenefit 
          title="Direct Comms Channel"
          description="JetSharing.co enables direct, encrypted communication with pilots, so you can discuss your companion's needs and make arrangements pre-flight."
          iconColor={THEME_COLORS.textPrimaryAccent}
        />
      </section>

      <section className="scroll-animation">
        <h2 className={`text-2xl font-bold text-center mb-6 ${THEME_COLORS.textSecondaryAccent}`}>Happy Companions, Harmonious Journeys</h2> {/* Compacted text-3xl, mb-8 */}
        <div className="grid md:grid-cols-2 gap-5"> {/* Compacted gap-6 */}
          {[
            { name: "Sarah & Unit 734 (Cyber-Mastiff)", quote: "Flying with Unit 734 was a dream! He interfaced directly with the cabin systems. No more commercial flight packet loss!", image: "https://picsum.photos/seed/petflyRed1/350/250" }, // Smaller image
            { name: "Mark & Luna (Bio-Engineered Feline)", quote: "Luna usually detests transit, but she was calm on our private vector. Being in-cabin made all the difference. Thanks, JetSharing.co!", image: "https://picsum.photos/seed/petflyRed2/350/250" }
          ].map((testimonial, index) => (
            <div key={index} className={`${THEME_COLORS.bgGlass} p-5 rounded-xl shadow-lg`}> {/* Compacted p-6 */}
              <img src={testimonial.image} alt={testimonial.name} className="w-full h-40 object-cover rounded-lg mb-3 border-2 ${THEME_COLORS.borderPrimaryAccent}/30"/> {/* Compacted h-48, mb-4 */}
              <blockquote className={`${THEME_COLORS.textMedium} italic text-xs mb-1.5`}>"{testimonial.quote}"</blockquote> {/* Compacted mb-2 */}
              <p className={`font-semibold ${THEME_COLORS.textPrimaryAccent} text-xs text-right`}>- {testimonial.name}</p>
            </div>
          ))}
        </div>
      </section>

      <section className={`text-center ${THEME_COLORS.bgGlass} p-6 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/10 scroll-animation`}> {/* Compacted p-8 */}
        <h2 className={`text-2xl font-bold mb-3 ${THEME_COLORS.gradientTextUser}`}>Ready for a Companion-Friendly Vector?</h2> {/* Compacted text-3xl, mb-4 */}
        <p className={`${THEME_COLORS.textMedium} text-sm mb-5`}> {/* Compacted mb-6 */}
          Utilize our "Pet Friendly" filter when scanning flight vectors to find pilots who accommodate all your cherished entities.
        </p>
        <Link
          to={`${ROUTES.SEARCH_FLIGHTS}?petFriendly=true`}
          className={`inline-block ${THEME_COLORS.gradientBgButton} text-white font-bold py-2.5 px-6 rounded-lg shadow-lg hover:shadow-xl ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} text-sm`} // Compacted py-3, px-8
        >
          Find Companion-Friendly Flights
        </Link>
      </section>
    </div>
  );
};

export default PetTravelPage;
