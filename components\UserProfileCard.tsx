
import React from 'react';
import { Link } from 'react-router-dom';
import { UserProfile, UserRole, BadgeType } from '../types';
import { ROUTES, THEME_COLORS } from '../constants';
import { SparklesIcon } from './icons/SparklesIcon';
import { PetIcon } from './icons/PetIcon'; 

interface UserProfileCardProps {
  user: UserProfile;
}

const BadgeDisplay: React.FC<{ badge: BadgeType }> = ({ badge }) => {
  let textAndBorderColor = THEME_COLORS.textMedium;
  let bgColor = 'bg-[var(--color-glass-bg)]/50';
  let icon = null;
  let borderColor = `${THEME_COLORS.borderPrimaryAccent}/30`;

  switch (badge) {
    case BadgeType.VerifiedPilot: 
      textAndBorderColor = THEME_COLORS.textPrimaryAccent; 
      icon = <SparklesIcon className="w-2.5 h-2.5 mr-1" />; 
      borderColor = `${THEME_COLORS.borderPrimaryAccent}/50`;
      bgColor = 'bg-[var(--color-primary-purple)]/10';
      break;
    case BadgeType.CryptoNative: 
      textAndBorderColor = THEME_COLORS.textSecondaryAccent;
      borderColor = `${THEME_COLORS.borderSecondaryAccent}/50`;
      bgColor = 'bg-[var(--color-secondary-red)]/10';
      break;
    case BadgeType.PetFriendlyHost: 
      textAndBorderColor = THEME_COLORS.textPrimaryAccent;
      icon = <PetIcon className="w-2.5 h-2.5 mr-1" />; 
      borderColor = `${THEME_COLORS.borderPrimaryAccent}/40`;
      bgColor = 'bg-[var(--color-primary-purple)]/15';
      break;
    case BadgeType.TopRated: 
      textAndBorderColor = THEME_COLORS.gradientTextUser; // Use gradient for special badge
      borderColor = `${THEME_COLORS.borderPrimaryAccent}/60`;
      bgColor = 'bg-transparent'; // Let gradient text shine
      break;
    case BadgeType.EarlyAdopter: 
      textAndBorderColor = THEME_COLORS.textMedium;
      borderColor = `border-[var(--color-text-dark)]/50`;
      bgColor = 'bg-[var(--color-text-dark)]/10';
      break;
  }
  return <span className={`text-2xs font-semibold px-1.5 py-0.5 rounded-full inline-flex items-center border ${borderColor} ${bgColor} ${textAndBorderColor}`}>{icon}{badge}</span>; // Compacted font, padding
};


const UserProfileCard: React.FC<UserProfileCardProps> = ({ user }) => {
  return (
    <div className={`${THEME_COLORS.bgGlass} rounded-xl shadow-lg hover:shadow-2xl hover:${THEME_COLORS.shadowPrimary} transition-all duration-300 p-4 flex flex-col items-center text-center scroll-animation`}> {/* Compacted p-6 to p-4 */}
      <img
        src={user.profilePictureUrl || `https://picsum.photos/seed/${user.id}/80/80`} // Compacted 100 to 80
        alt={user.name}
        className={`w-20 h-20 rounded-full border-2 ${THEME_COLORS.borderPrimaryAccent} object-cover mb-3`} // Compacted w-24 h-24, mb-4
      />
      <h3 className={`text-lg font-bold ${THEME_COLORS.gradientTextUser} mb-0.5`}>{user.name}</h3> {/* Compacted text-xl, mb-1 */}
      <p className={`text-xs ${THEME_COLORS.textMedium} mb-0.5`}>{user.role === UserRole.Pilot ? `Pilot (${user.pilotRating} ★)` : 'Traveler'}</p> {/* Compacted text-sm, mb-1 */}
      {user.location && <p className={`text-2xs ${THEME_COLORS.textDark} mb-2`}>{user.location}</p>} {/* Compacted text-xs, mb-3 */}
      
      {user.bio && <p className={`text-2xs ${THEME_COLORS.textMedium} mb-3 h-10 overflow-hidden text-ellipsis`}>{user.bio}</p>} {/* Compacted text-xs, h-12, mb-4 */}

      <div className="flex flex-wrap gap-1 justify-center mb-3"> {/* Compacted mb-4 */}
        {user.badges.slice(0, 3).map(badge => <BadgeDisplay key={badge} badge={badge} />)}
      </div>
      
      <Link
        to={`${ROUTES.USER_PROFILE}/${user.id}`}
        className={`mt-auto w-full ${THEME_COLORS.gradientBgButton} text-white font-semibold py-1.5 px-3 border border-transparent rounded-lg shadow-md hover:shadow-lg ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} text-xs`} // Compacted py-2, text-sm
      >
        View Profile
      </Link>
    </div>
  );
};

export default UserProfileCard;
