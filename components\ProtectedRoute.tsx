import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ROUTES } from '../constants';

interface ProtectedRouteProps {
  children: JSX.Element;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, currentUser } = useAuth();
  const location = useLocation();

  if (!isAuthenticated) {
    // Redirect them to the /login page, but save the current location they were
    // trying to go to when they were redirected. This allows us to send them
    // along to that page after they login, which is a nicer user experience
    // than dropping them off on the home page.
    return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />;
  }
  
  // Add role-based protection if needed in the future
  // For example, if trying to access PilotDashboard but current user is Traveler
  // if (location.pathname === ROUTES.PILOT_DASHBOARD && currentUser?.role !== UserRole.Pilot) {
  //   return <Navigate to={ROUTES.TRAVELER_DASHBOARD} replace />;
  // }
  // if (location.pathname === ROUTES.TRAVELER_DASHBOARD && currentUser?.role !== UserRole.Traveler) {
  //    return <Navigate to={ROUTES.PILOT_DASHBOARD} replace />;
  // }


  return children;
};

export default ProtectedRoute;