
import React from 'react';
import { THEME_COLORS } from '../constants';
import { WalletIcon } from '../components/icons/WalletIcon';

const CryptoPaymentsPage: React.FC = () => {
  return (
    <div className={`max-w-2xl mx-auto ${THEME_COLORS.bgGlass} p-5 md:p-8 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/20 scroll-animation`}> {/* Compacted max-w-3xl, p-6/10 */}
      <div className="flex items-center mb-5"> {/* Compacted mb-6 */}
        <WalletIcon className={`w-10 h-10 mr-3 ${THEME_COLORS.textPrimaryAccent}`} /> {/* Compacted w-12 h-12, mr-4 */}
        <h1 className={`text-2xl md:text-3xl font-bold ${THEME_COLORS.gradientTextUser}`}>Crypto Payment Protocol</h1> {/* Compacted text-3xl/4xl */}
      </div>
      
      <div className={`space-y-3.5 ${THEME_COLORS.textMedium} text-sm`}> {/* Compacted space-y-4 */}
        <p>JetSharing.co embraces cryptocurrency payments for specified services and transactions. These Crypto Payment Terms augment our main Terms of Service.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textSecondaryAccent}`}>1. Accepted Digital Assets</h2> {/* Compacted text-xl, mt-6, mb-2 */}
        <p>The Platform may support payments in various crypto-assets like Bitcoin (BTC), Ethereum (ETH), and select stablecoins (e.g., USDC, DAI). The roster of accepted assets is dynamic and subject to platform evolution.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textSecondaryAccent}`}>2. Wallet Sovereignty</h2>
        <p>You bear sole responsibility for your cryptocurrency wallet(s) security and management. JetSharing.co NEVER holds or requests your private keys. Employ secure wallets and adhere to OpSec best practices. We are not liable for losses from compromised wallets or user error.</p>
        
        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textSecondaryAccent}`}>3. Transaction Mechanics</h2>
        <p>When transacting with crypto:</p>
        <ul className="list-disc list-inside ml-3 space-y-0.5 text-xs"> {/* Compacted ml-4, space-y-1 */}
          <li>Crypto price is displayed at transaction time, often via oracle feed to a fiat peg (e.g., USD). This rate is volatile.</li>
          <li>A specific wallet address will be provided for sending the exact crypto amount. TRIPLE-CHECK all details (amount, currency, address, network) before broadcasting.</li>
          <li>Blockchain transactions are generally immutable post-confirmation.</li>
        </ul>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textSecondaryAccent}`}>4. Network Fees (Gas)</h2>
        <p>Blockchain network transaction fees (e.g., Ethereum gas) are your responsibility and not set/collected by JetSharing.co. Ensure sufficient wallet balance for payment AND network fees.</p>
        
        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textSecondaryAccent}`}>5. Price Volatility Disclaimer</h2>
        <p>Cryptocurrencies are inherently volatile. Fiat value can fluctuate wildly. Crypto prices for services are subject to this until transaction confirmation. Confirmed crypto amounts are fixed for that transaction.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textSecondaryAccent}`}>6. Refund Parameters</h2>
        <p>Crypto payment refund policies are per specific service/flight terms, potentially governed by smart contract logic. Refunds, if issued, may be in original crypto or fiat equivalent at time of refund, per provider/our discretion. Network fees are typically non-refundable.</p>
        
        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textSecondaryAccent}`}>7. Regulatory Compliance & Taxation</h2>
        <p>You are responsible for adhering to all applicable laws regarding crypto use in your jurisdiction, including tax obligations.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textSecondaryAccent}`}>8. No Financial Alchemy</h2>
        <p>Information on JetSharing.co about crypto is for informational purposes, not financial advice. We don't endorse specific assets. DYOR (Do Your Own Research) before any crypto transaction.</p>

        <p className={`mt-6 text-xs ${THEME_COLORS.textDark}`}>By utilizing crypto on JetSharing.co, you acknowledge and accept these risks and terms.</p> {/* Compacted mt-8, text-sm */}
        <p className={`mt-1.5 text-xs ${THEME_COLORS.textDark}`}>Last Synced: {new Date().toLocaleDateString()}</p> {/* Compacted mt-2 */}
      </div>
    </div>
  );
};

export default CryptoPaymentsPage;
