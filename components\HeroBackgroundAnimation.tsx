
import React, { useEffect, useRef, useCallback } from 'react';

// Define theme colors matching CSS variables (or pass as props)
const PARTICLE_COLORS = [
  '#00D4FF', // Cyan
  '#FF6B9D', // Pink
  '#9B59B6', // Purple
  '#3498DB', // Blue
  '#E74C3C', // Red
  '#F39C12', // Orange
  '#2ECC71', // Green
  '#1ABC9C', // Turquoise
]; // Modern vibrant colors

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
  opacity: number;
  life: number; // Max lifetime
  initialLife: number; // To calculate opacity fade
}

const HeroBackgroundAnimation: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const particlesArrayRef = useRef<Particle[]>([]);
  const mousePositionRef = useRef<{ x: number | null, y: number | null }>({ x: null, y: null });

  const createParticle = useCallback((canvas: HTMLCanvasElement): Particle => {
    const life = Math.random() * 60 * 4 + 60 * 3; // Lifetime in frames (3-7 seconds at 60fps)
    const dpr = window.devicePixelRatio || 1;
    return {
      x: Math.random() * (canvas.width / dpr),
      y: Math.random() * (canvas.height / dpr),
      size: Math.random() * 3 + 1, // Slightly larger particles
      speedX: (Math.random() - 0.5) * 0.8, // More movement
      speedY: (Math.random() - 0.5) * 0.8, // More movement
      color: PARTICLE_COLORS[Math.floor(Math.random() * PARTICLE_COLORS.length)],
      opacity: 0, // Start invisible, fade in
      life: life,
      initialLife: life,
    };
  }, []);

  const initParticles = useCallback((canvas: HTMLCanvasElement) => {
    const dpr = window.devicePixelRatio || 1;
    const area = (canvas.width / dpr) * (canvas.height / dpr);
    const numberOfParticles = Math.min(200, Math.max(50, Math.floor(area / 8000))); // Better density control
    particlesArrayRef.current = [];
    for (let i = 0; i < numberOfParticles; i++) {
      particlesArrayRef.current.push(createParticle(canvas));
    }
  }, [createParticle]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const setCanvasDimensions = () => {
        const dpr = window.devicePixelRatio || 1;
        // Set canvas size based on its offsetParent's dimensions or window for full screen effect
        const parent = canvas.offsetParent as HTMLElement;
        if (parent) {
            canvas.width = parent.offsetWidth * dpr;
            canvas.height = parent.offsetHeight * dpr;
        } else {
            // Fallback if no offsetParent, e.g. for absolute positioned canvas taking full viewport
            canvas.width = window.innerWidth * dpr;
            canvas.height = window.innerHeight * dpr;
        }
        canvas.style.width = `${canvas.width / dpr}px`;
        canvas.style.height = `${canvas.height / dpr}px`;
        ctx.scale(dpr, dpr);
    };


    const handleMouseMove = (event: MouseEvent) => {
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        mousePositionRef.current.x = (event.clientX - rect.left) * (canvas.width / rect.width / (window.devicePixelRatio || 1));
        mousePositionRef.current.y = (event.clientY - rect.top) * (canvas.height / rect.height / (window.devicePixelRatio || 1));
      }
    };
    window.addEventListener('mousemove', handleMouseMove);


    const animate = () => {
      ctx.clearRect(0, 0, canvas.width / (window.devicePixelRatio || 1), canvas.height / (window.devicePixelRatio || 1));
      
      particlesArrayRef.current.forEach((particle, index) => {
        // Update particle
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        particle.life -= 1;

        // Fade in/out logic
        if (particle.life > particle.initialLife * 0.9) { // Fade in for first 10%
          particle.opacity = Math.min(1, particle.opacity + 0.05);
        } else if (particle.life < particle.initialLife * 0.2) { // Fade out for last 20%
          particle.opacity = Math.max(0, particle.opacity - 0.05);
        } else {
            particle.opacity = Math.min(1, particle.opacity + 0.02); // Ensure it reaches full opacity if not fading out
        }


        // Boundary check or respawn
        if (particle.life <= 0 || particle.opacity <= 0) {
          particlesArrayRef.current[index] = createParticle(canvas);
        } else {
            if (particle.x > canvas.width / (window.devicePixelRatio || 1) || particle.x < 0) particle.speedX *= -1;
            if (particle.y > canvas.height / (window.devicePixelRatio || 1) || particle.y < 0) particle.speedY *= -1;
        }
        
        // Mouse interaction: push particles away slightly
        if (mousePositionRef.current.x !== null && mousePositionRef.current.y !== null) {
            const dx = particle.x - mousePositionRef.current.x;
            const dy = particle.y - mousePositionRef.current.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const forceDirectionX = dx / distance;
            const forceDirectionY = dy / distance;
            const maxDistance = 50; // Interaction radius
            const force = (maxDistance - distance) / maxDistance; // Stronger force closer to mouse

            if (distance < maxDistance) {
                const pushStrength = 0.05; // How much particles are pushed
                particle.x += forceDirectionX * force * pushStrength;
                particle.y += forceDirectionY * force * pushStrength;
            }
        }


        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity * 0.8;
        ctx.fill();

        // Add glow effect
        ctx.shadowColor = particle.color;
        ctx.shadowBlur = particle.size * 2;
        ctx.fill();
        ctx.shadowBlur = 0;

        ctx.globalAlpha = 1; // Reset globalAlpha
      });

      // Draw connections between nearby particles
      ctx.strokeStyle = 'rgba(0, 212, 255, 0.2)';
      ctx.lineWidth = 0.5;
      ctx.globalAlpha = 0.3;

      for (let i = 0; i < particlesArrayRef.current.length; i++) {
        for (let j = i + 1; j < particlesArrayRef.current.length; j++) {
          const p1 = particlesArrayRef.current[i];
          const p2 = particlesArrayRef.current[j];
          const distance = Math.sqrt((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2);

          if (distance < 120 && p1.opacity > 0.3 && p2.opacity > 0.3) {
            ctx.globalAlpha = (120 - distance) / 120 * 0.2;
            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.stroke();
          }
        }
      }

      ctx.globalAlpha = 1; // Reset globalAlpha

      animationFrameIdRef.current = requestAnimationFrame(animate);
    };
    
    const handleResize = () => {
        setCanvasDimensions();
        initParticles(canvas);
    };

    setCanvasDimensions();
    initParticles(canvas);
    animate();
    window.addEventListener('resize', handleResize);

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [initParticles, createParticle]);

  return (
    <canvas
      ref={canvasRef}
      id="hero-canvas-animation"
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{
        background: 'linear-gradient(135deg, rgba(16, 16, 32, 0.95) 0%, rgba(32, 16, 64, 0.9) 50%, rgba(16, 32, 48, 0.95) 100%)',
        zIndex: 1
      }}
    />
  );
};

export default HeroBackgroundAnimation;
