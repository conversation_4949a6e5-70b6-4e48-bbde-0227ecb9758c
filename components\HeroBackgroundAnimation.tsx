
import React, { useEffect, useRef, useCallback } from 'react';

// Define theme colors matching CSS variables (or pass as props)
const PARTICLE_COLORS = ['#9D00FF', '#FF003C', '#F02D7A', '#7A00E0']; // Purple, Red, Pink, Darker Purple

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
  opacity: number;
  life: number; // Max lifetime
  initialLife: number; // To calculate opacity fade
}

const HeroBackgroundAnimation: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const particlesArrayRef = useRef<Particle[]>([]);
  const mousePositionRef = useRef<{ x: number | null, y: number | null }>({ x: null, y: null });

  const createParticle = useCallback((canvas: HTMLCanvasElement): Particle => {
    const life = Math.random() * 60 * 3 + 60 * 2; // Lifetime in frames (2-5 seconds at 60fps)
    return {
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      size: Math.random() * 2 + 0.5, // Particle size
      speedX: (Math.random() - 0.5) * 0.5, // Slowed down
      speedY: (Math.random() - 0.5) * 0.5, // Slowed down
      color: PARTICLE_COLORS[Math.floor(Math.random() * PARTICLE_COLORS.length)],
      opacity: 0, // Start invisible, fade in
      life: life,
      initialLife: life,
    };
  }, []);

  const initParticles = useCallback((canvas: HTMLCanvasElement) => {
    const numberOfParticles = Math.floor((canvas.width * canvas.height) / 15000); // Density based on area
    particlesArrayRef.current = [];
    for (let i = 0; i < numberOfParticles; i++) {
      particlesArrayRef.current.push(createParticle(canvas));
    }
  }, [createParticle]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const setCanvasDimensions = () => {
        const dpr = window.devicePixelRatio || 1;
        // Set canvas size based on its offsetParent's dimensions or window for full screen effect
        const parent = canvas.offsetParent as HTMLElement;
        if (parent) {
            canvas.width = parent.offsetWidth * dpr;
            canvas.height = parent.offsetHeight * dpr;
        } else {
            // Fallback if no offsetParent, e.g. for absolute positioned canvas taking full viewport
            canvas.width = window.innerWidth * dpr;
            canvas.height = window.innerHeight * dpr;
        }
        canvas.style.width = `${canvas.width / dpr}px`;
        canvas.style.height = `${canvas.height / dpr}px`;
        ctx.scale(dpr, dpr);
    };


    const handleMouseMove = (event: MouseEvent) => {
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        mousePositionRef.current.x = (event.clientX - rect.left) * (canvas.width / rect.width / (window.devicePixelRatio || 1));
        mousePositionRef.current.y = (event.clientY - rect.top) * (canvas.height / rect.height / (window.devicePixelRatio || 1));
      }
    };
    window.addEventListener('mousemove', handleMouseMove);


    const animate = () => {
      ctx.clearRect(0, 0, canvas.width / (window.devicePixelRatio || 1), canvas.height / (window.devicePixelRatio || 1));
      
      particlesArrayRef.current.forEach((particle, index) => {
        // Update particle
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        particle.life -= 1;

        // Fade in/out logic
        if (particle.life > particle.initialLife * 0.9) { // Fade in for first 10%
          particle.opacity = Math.min(1, particle.opacity + 0.05);
        } else if (particle.life < particle.initialLife * 0.2) { // Fade out for last 20%
          particle.opacity = Math.max(0, particle.opacity - 0.05);
        } else {
            particle.opacity = Math.min(1, particle.opacity + 0.02); // Ensure it reaches full opacity if not fading out
        }


        // Boundary check or respawn
        if (particle.life <= 0 || particle.opacity <= 0) {
          particlesArrayRef.current[index] = createParticle(canvas);
        } else {
            if (particle.x > canvas.width / (window.devicePixelRatio || 1) || particle.x < 0) particle.speedX *= -1;
            if (particle.y > canvas.height / (window.devicePixelRatio || 1) || particle.y < 0) particle.speedY *= -1;
        }
        
        // Mouse interaction: push particles away slightly
        if (mousePositionRef.current.x !== null && mousePositionRef.current.y !== null) {
            const dx = particle.x - mousePositionRef.current.x;
            const dy = particle.y - mousePositionRef.current.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const forceDirectionX = dx / distance;
            const forceDirectionY = dy / distance;
            const maxDistance = 50; // Interaction radius
            const force = (maxDistance - distance) / maxDistance; // Stronger force closer to mouse

            if (distance < maxDistance) {
                const pushStrength = 0.05; // How much particles are pushed
                particle.x += forceDirectionX * force * pushStrength;
                particle.y += forceDirectionY * force * pushStrength;
            }
        }


        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();
        ctx.globalAlpha = 1; // Reset globalAlpha
      });

      animationFrameIdRef.current = requestAnimationFrame(animate);
    };
    
    const handleResize = () => {
        setCanvasDimensions();
        initParticles(canvas);
    };

    setCanvasDimensions();
    initParticles(canvas);
    animate();
    window.addEventListener('resize', handleResize);

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [initParticles, createParticle]);

  return <canvas ref={canvasRef} id="hero-canvas-animation" />;
};

export default HeroBackgroundAnimation;
