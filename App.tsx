
import React from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import Header from './components/Header';
import Footer from './components/Footer';
import ParticleBackground from './components/ParticleBackground';
import ScrollToTop from './components/ScrollToTop';
import HomePage from './pages/HomePage';
import FlightSearchPage from './pages/FlightSearchPage';
import UserProfilePage from './pages/UserProfilePage';
import ListLegPage from './pages/ListLegPage';
import MembershipPage from './pages/MembershipPage';
import MembersDirectoryPage from './pages/MembersDirectoryPage';
import SafetyPage from './pages/SafetyPage';
import PetTravelPage from './pages/PetTravelPage';
import TermsPage from './pages/TermsPage';
import PrivacyPage from './pages/PrivacyPage';
import FaaDisclaimerPage from './pages/FaaDisclaimerPage';
import CryptoPaymentsPage from './pages/CryptoPaymentsPage';
import NotFoundPage from './pages/NotFoundPage';
import LoginPage from './pages/LoginPage';
import PilotDashboardPage from './pages/PilotDashboardPage';
import TravelerDashboardPage from './pages/TravelerDashboardPage';
import ProtectedRoute from './components/ProtectedRoute';
import { ROUTES, THEME_COLORS } from './constants';

const App: React.FC = () => {
  return (
    <HashRouter>
      <div className="min-h-screen flex flex-col relative overflow-hidden">
        {/* Enhanced background with gradient */}
        <div className="fixed inset-0 bg-gradient-to-br from-gray-900 via-purple-900/20 to-black" />

        {/* Particle background */}
        <ParticleBackground
          density={30}
          speed={0.3}
          colors={['#9D00FF', '#FF003C', '#F02D7A', '#00FFFF']}
          className="fixed inset-0 z-0"
          interactive={true}
        />

        {/* Content */}
        <div className="relative z-10 min-h-screen flex flex-col text-white">
          <Header />
          <motion.main
            className="flex-grow container mx-auto px-4 py-6 pt-24"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
          >
          <Routes>
            <Route path={ROUTES.HOME} element={<HomePage />} />
            <Route path={ROUTES.LOGIN} element={<LoginPage />} />
            <Route path={ROUTES.SEARCH_FLIGHTS} element={<FlightSearchPage />} />
            <Route path={`${ROUTES.USER_PROFILE}/:userId`} element={<UserProfilePage />} />
            <Route path={ROUTES.LIST_LEG} element={<ListLegPage />} />
            <Route path={ROUTES.MEMBERSHIP} element={<MembershipPage />} />
            <Route path={ROUTES.MEMBERS_DIRECTORY} element={<MembersDirectoryPage />} />
            <Route path={ROUTES.SAFETY} element={<SafetyPage />} />
            <Route path={ROUTES.PET_TRAVEL} element={<PetTravelPage />} />
            <Route path={ROUTES.TERMS} element={<TermsPage />} />
            <Route path={ROUTES.PRIVACY} element={<PrivacyPage />} />
            <Route path={ROUTES.FAA_DISCLAIMER} element={<FaaDisclaimerPage />} />
            <Route path={ROUTES.CRYPTO_PAYMENTS} element={<CryptoPaymentsPage />} />
            
            <Route 
              path={ROUTES.PILOT_DASHBOARD} 
              element={
                <ProtectedRoute>
                  <PilotDashboardPage />
                </ProtectedRoute>
              } 
            />
            <Route 
              path={ROUTES.TRAVELER_DASHBOARD} 
              element={
                <ProtectedRoute>
                  <TravelerDashboardPage />
                </ProtectedRoute>
              } 
            />

            <Route path="/404" element={<NotFoundPage />} />
            <Route path="*" element={<Navigate replace to="/404" />} />
          </Routes>
          </motion.main>
          <Footer />

          {/* Scroll to top button */}
          <ScrollToTop variant="rocket" threshold={300} />
        </div>
      </div>
    </HashRouter>
  );
};

export default App;
