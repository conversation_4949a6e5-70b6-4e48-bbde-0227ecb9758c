
import React from 'react';
import { THEME_COLORS } from '../constants';
import { ShieldCheckIcon } from '../components/icons/ShieldCheckIcon';

const SafetyFeature: React.FC<{ title: string; description: string; iconColorClass: string }> = ({ title, description, iconColorClass }) => (
    <div className={`${THEME_COLORS.bgGlass} p-5 rounded-xl shadow-lg hover:${THEME_COLORS.shadowPrimary}/30 transition-shadow duration-300 scroll-animation`}> {/* Compacted p-6 to p-5 */}
        <ShieldCheckIcon className={`w-10 h-10 mb-3 ${iconColorClass}`} /> {/* Compacted w-12 h-12, mb-4 */}
        <h3 className={`text-xl font-semibold mb-1.5 ${THEME_COLORS.gradientTextUser}`}>{title}</h3> {/* Compacted text-2xl, mb-2 */}
        <p className={`${THEME_COLORS.textMedium} text-xs`}>{description}</p> {/* Compacted to text-xs */}
    </div>
);

const SafetyPage: React.FC = () => {
  return (
    <div className="max-w-3xl mx-auto space-y-10"> {/* Compacted max-w-4xl, space-y-12 */}
      <section className="text-center scroll-animation">
        <ShieldCheckIcon className={`w-20 h-20 mx-auto mb-5 ${THEME_COLORS.textPrimaryAccent}`} /> {/* Compacted w-24 h-24, mb-6 */}
        <h1 className={`text-3xl md:text-4xl font-bold ${THEME_COLORS.gradientTextUser} mb-3`}>Safety & Verification Protocols</h1> {/* Compacted text-4xl/5xl, mb-4 */}
        <p className={`text-base md:text-lg ${THEME_COLORS.textMedium} max-w-xl mx-auto`}> {/* Compacted max-w-2xl */}
          At JetSharing.co, your security and trust are encoded into our core. We're committed to robust verification and fostering a secure, decentralized aviation community.
        </p>
      </section>

      <section className="grid md:grid-cols-2 gap-6"> {/* Compacted gap-8 */}
        <SafetyFeature 
          title="Pilot Credentialing"
          description="Pilots undergo verification of licenses, certifications, and identity. Aiming for on-chain credential attestations for enhanced, immutable transparency."
          iconColorClass={THEME_COLORS.textPrimaryAccent}
        />
        <SafetyFeature 
          title="Aircraft Airworthiness"
          description="Pilots provide documentation for aircraft airworthiness and maintenance. We champion radical transparency regarding safety protocols."
          iconColorClass={THEME_COLORS.textPrimaryAccent}
        />
        <SafetyFeature 
          title="In-Flight Insurance Intel"
          description="Pilots maintain appropriate insurance. Travelers are advised to query specific coverage parameters for each flight vector."
          iconColorClass={THEME_COLORS.textSecondaryAccent}
        />
        <SafetyFeature 
          title="Community & Reputation DAO"
          description="Our platform's rating and review system, potentially migrating to a DAO structure, fosters accountability and elite operational standards."
          iconColorClass={THEME_COLORS.textSecondaryAccent}
        />
        <SafetyFeature 
          title="Secure Crypto Transactions"
          description="Crypto payments utilize best-in-class security. Always verify wallet addresses and practice diligent OpSec with your private keys."
          iconColorClass={THEME_COLORS.textPrimaryAccent}
        />
        <SafetyFeature 
          title="Data Privacy & Sovereignty"
          description="Committed to protecting your data. Our privacy protocol details how we handle information, prioritizing user sovereignty."
          iconColorClass={THEME_COLORS.textPrimaryAccent}
        />
      </section>

      <section className={`${THEME_COLORS.bgGlass} p-6 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/10 text-center scroll-animation`}> {/* Compacted p-8 */}
        <h2 className={`text-2xl font-bold mb-3 ${THEME_COLORS.gradientTextUser}`}>Our Immutable Commitment</h2> {/* Compacted text-3xl, mb-4 */}
        <p className={`${THEME_COLORS.textMedium} text-sm mb-3`}> {/* Compacted mb-4 */}
          JetSharing.co endeavors to architect a trustless environment for shared private aviation. While we deploy verification measures, users must always exercise due diligence (DYOR) and communicate with radical clarity.
        </p>
        <p className={`font-semibold ${THEME_COLORS.textSecondaryAccent} text-sm`}>
          Fly smart, fly sovereign with JetSharing.co.
        </p>
      </section>
    </div>
  );
};

export default SafetyPage;
