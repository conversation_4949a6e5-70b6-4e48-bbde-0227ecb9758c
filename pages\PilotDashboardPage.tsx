
import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { THEME_COLORS, ROUTES } from '../constants';
import { PlaneIcon } from '../components/icons/PlaneIcon'; 
import { UserGroupIcon } from '../components/icons/UserGroupIcon';
import { SparklesIcon } from '../components/icons/SparklesIcon';

const DashboardCard: React.FC<{ title: string; description: string; to: string; icon: React.ReactNode; colorTheme: 'purple' | 'red' | 'neutral' }> = 
  ({ title, description, to, icon, colorTheme }) => {
  
  let accentColorClass = THEME_COLORS.textMedium;
  let hoverShadowClass = `hover:${THEME_COLORS.shadowPrimary}/30`;
  let borderColorClass = `${THEME_COLORS.borderPrimaryAccent}/30`;
  let iconBgClass = `bg-[var(--color-dark-bg)]/60`;

  if (colorTheme === 'purple') {
    accentColorClass = THEME_COLORS.textPrimaryAccent;
    hoverShadowClass = `hover:${THEME_COLORS.shadowPrimary}/50`;
    borderColorClass = `${THEME_COLORS.borderPrimaryAccent}/50`;
    iconBgClass = `bg-[var(--color-primary-purple)]/15`;
  } else if (colorTheme === 'red') {
    accentColorClass = THEME_COLORS.textSecondaryAccent;
    hoverShadowClass = `hover:${THEME_COLORS.shadowSecondary}/50`;
    borderColorClass = `${THEME_COLORS.borderSecondaryAccent}/50`;
    iconBgClass = `bg-[var(--color-secondary-red)]/15`;
  }
  
  return (
    <Link 
      to={to} 
      className={`block ${THEME_COLORS.bgGlass} p-5 rounded-xl shadow-lg ${hoverShadowClass} transition-all duration-300 hover:scale-105 text-center border ${borderColorClass}`} // Compacted p-6
    >
      <div className={`mx-auto mb-3 w-10 h-10 flex items-center justify-center rounded-full ${iconBgClass} border ${borderColorClass}`}> {/* Compacted w-12 h-12, mb-4 */}
        {icon}
      </div>
      <h3 className={`text-xl font-semibold mb-1.5 ${accentColorClass}`}>{title}</h3> {/* Compacted text-2xl, mb-2 */}
      <p className={`${THEME_COLORS.textMedium} text-xs`}>{description}</p> {/* Compacted text-sm */}
    </Link>
  );
};


const PilotDashboardPage: React.FC = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <p className={`${THEME_COLORS.textPrimaryAccent} text-center text-sm`}>Loading pilot datastream... stand by.</p>; // Compacted text
  }

  return (
    <div className="space-y-10"> {/* Compacted space-y-12 */}
      <section className="text-center scroll-animation">
        <h1 className={`text-3xl md:text-4xl font-bold ${THEME_COLORS.gradientTextUser} mb-3`}> {/* Compacted text-4xl/5xl, mb-4 */}
          Pilot Command Deck
        </h1>
        <p className={`text-base md:text-lg ${THEME_COLORS.textMedium} max-w-xl mx-auto`}> {/* Compacted max-w-2xl */}
          Welcome, {currentUser.name}! Manage flight manifests, profile, and fleet data.
        </p>
      </section>

      <section className="grid md:grid-cols-2 lg:grid-cols-3 gap-5 scroll-animation"> {/* Compacted gap-6 */}
        <DashboardCard 
            title="Manifest New Flight" 
            description="Broadcast available empty leg vectors to the network."
            to={ROUTES.LIST_LEG}
            icon={<PlaneIcon className={`w-5 h-5 ${THEME_COLORS.textPrimaryAccent}`}/>} // Compacted w-6 h-6
            colorTheme="purple"
        />
        <DashboardCard 
            title="My Pilot Profile" 
            description="Update aircraft details, certifications, and public data."
            to={`${ROUTES.USER_PROFILE}/${currentUser.id}`}
            icon={<UserGroupIcon className={`w-5 h-5 ${THEME_COLORS.textMedium}`}/>}
            colorTheme="neutral"
        />
         <DashboardCard 
            title="Active Manifests" 
            description="View and manage current flight listings. (Coming Soon)"
            to="#" 
            icon={<SparklesIcon className={`w-5 h-5 ${THEME_COLORS.textSecondaryAccent}`}/>}
            colorTheme="red"
        />
      </section>
      
       <section className={`${THEME_COLORS.bgGlass} p-6 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/20 text-center scroll-animation mt-10`}> {/* Compacted p-8, mt-12 */}
        <h2 className={`text-2xl font-bold mb-3 ${THEME_COLORS.textSecondaryAccent}`}>Pilot Metrics (Simulated)</h2> {/* Compacted text-3xl, mb-4 */}
        <p className={`${THEME_COLORS.textMedium} text-sm`}>Total flights manifested: <span className={THEME_COLORS.textPrimaryAccent}>12</span></p>
        <p className={`${THEME_COLORS.textMedium} text-sm`}>Average rating: <span className={THEME_COLORS.textPrimaryAccent}>{currentUser.pilotRating} ★</span></p>
        <p className={`${THEME_COLORS.textDark} text-xs mt-1.5`}>(Platform statistics are for demonstration.)</p>
      </section>
    </div>
  );
};

export default PilotDashboardPage;
