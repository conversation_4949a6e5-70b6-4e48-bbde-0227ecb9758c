
import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ROUTES, THEME_COLORS, MOCK_USERS } from '../constants';
import { UserRole } from '../types';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState(''); 
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleLoginProcess = async (loginEmail: string, loginPassword?: string) => {
    setError('');
    setIsLoading(true);
    try {
      const user = await login(loginEmail, loginPassword); 
      if (user) {
        if (user.role === UserRole.Pilot) {
          navigate(ROUTES.PILOT_DASHBOARD);
        } else {
          navigate(ROUTES.TRAVELER_DASHBOARD);
        }
      } else {
        setError('Invalid credentials. Please retry or use a demo identity.');
      }
    } catch (err) {
      console.error("Login error:", err);
      setError('An unexpected error occurred during authentication.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleLoginProcess(email, password);
  };

  const handleDemoLogin = (role: UserRole) => {
    let demoUser;
    if (role === UserRole.Pilot) {
      demoUser = MOCK_USERS.find(u => u.role === UserRole.Pilot && u.email);
    } else {
      demoUser = MOCK_USERS.find(u => u.role === UserRole.Traveler && u.email);
    }

    if (demoUser && demoUser.email) {
      handleLoginProcess(demoUser.email, 'password123'); 
    } else {
      setError(`Could not find a demo ${role.toLowerCase()} identity with an email.`);
    }
  };

  const inputBaseClasses = `appearance-none relative block w-full px-3 py-2.5 border placeholder-[var(--color-text-dark)] ${THEME_COLORS.textLight} bg-[var(--color-input-bg)] focus:outline-none focus:ring-1 sm:text-sm`; // Compacted py-3
  const inputFocusClasses = `focus:${THEME_COLORS.ringPrimaryAccent} focus:${THEME_COLORS.borderPrimaryAccent} focus:z-10`;

  return (
    <div className="min-h-[calc(100vh-12rem)] flex items-center justify-center py-10 px-4 sm:px-6 lg:px-8"> {/* Compacted min-h, py */}
      <div className={`max-w-sm w-full space-y-6 ${THEME_COLORS.bgGlass} p-6 md:p-8 rounded-xl shadow-2xl ${THEME_COLORS.shadowPrimary}/40`}> {/* Compacted max-w-md, space-y-8, p-8/10 */}
        <div>
          <h2 className={`mt-4 text-center text-2xl font-extrabold ${THEME_COLORS.gradientTextUser}`}> {/* Compacted mt-6, text-3xl */}
            Interface with JetSharing.co
          </h2>
          <p className={`mt-1.5 text-center text-xs ${THEME_COLORS.textMedium}`}> {/* Compacted mt-2, text-sm */}
            Or{' '}
            <Link to={ROUTES.MEMBERSHIP} className={`${THEME_COLORS.textPrimaryAccent} hover:underline font-medium`}>
              explore access tiers
            </Link>
          </p>
        </div>
        <form className="mt-6 space-y-5" onSubmit={handleSubmit}> {/* Compacted mt-8, space-y-6 */}
          <input type="hidden" name="remember" defaultValue="true" />
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email-address" className="sr-only">Email Address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`${inputBaseClasses} ${THEME_COLORS.borderPrimaryAccent}/40 rounded-t-md ${inputFocusClasses}`}
                placeholder="Email Address"
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">Password</label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={`${inputBaseClasses} ${THEME_COLORS.borderPrimaryAccent}/40 rounded-b-md ${inputFocusClasses}`}
                placeholder="Password"
              />
            </div>
          </div>

          {error && (
            <p className={`text-xs text-red-400 text-center bg-red-900/40 p-1.5 rounded-md border border-red-700/60`}>{error}</p> /* Compacted text-sm, p-2 */
          )}

          <div className="flex items-center justify-between">
            <div className="text-xs"> {/* Compacted text-sm */}
              <span className={`${THEME_COLORS.textSecondaryAccent} font-medium cursor-not-allowed`}>
                Forgot password? (Recovery via ENS soon)
              </span>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className={`group relative w-full flex justify-center py-2.5 px-4 border border-transparent text-xs font-medium rounded-md text-white ${isLoading ? 'bg-gray-700 cursor-not-allowed' : `${THEME_COLORS.gradientBgButton} ${THEME_COLORS.buttonGlow} focus:ring-[var(--color-primary-purple)]`} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[var(--color-dark-bg)] transition-colors duration-300`} // Compacted py-3, text-sm
            >
              {isLoading ? (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"> {/* Compacted size */}
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                'Authenticate'
              )}
            </button>
          </div>
        </form>
        
        <div className="mt-5 space-y-2.5"> {/* Compacted mt-6, space-y-3 */}
            <p className={`text-center text-xs ${THEME_COLORS.textMedium}`}>Or try a quick simulation:</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2.5"> {/* Compacted gap-3 */}
                 <button
                    onClick={() => handleDemoLogin(UserRole.Pilot)}
                    disabled={isLoading}
                    className={`w-full flex justify-center py-2 px-3 border ${THEME_COLORS.borderSecondaryAccent}/60 text-xs font-medium rounded-md ${THEME_COLORS.textSecondaryAccent} ${isLoading ? 'bg-gray-800 cursor-not-allowed' : `bg-[var(--color-input-bg)] hover:bg-[var(--color-secondary-red)]/20 ${THEME_COLORS.buttonGlowSecondary}`} focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-offset-[var(--color-dark-bg)] ${THEME_COLORS.ringSecondaryAccent} transition-colors duration-300`} // Compacted sizes & styles
                    >
                    Simulate Pilot Login
                </button>
                <button
                    onClick={() => handleDemoLogin(UserRole.Traveler)}
                    disabled={isLoading}
                    className={`w-full flex justify-center py-2 px-3 border ${THEME_COLORS.borderPrimaryAccent}/60 text-xs font-medium rounded-md ${THEME_COLORS.textPrimaryAccent} ${isLoading ? 'bg-gray-800 cursor-not-allowed' : `bg-[var(--color-input-bg)] hover:bg-[var(--color-primary-purple)]/20 ${THEME_COLORS.buttonGlow}`} focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-offset-[var(--color-dark-bg)] ${THEME_COLORS.ringPrimaryAccent} transition-colors duration-300`}
                    >
                    Simulate Traveler Login
                </button>
            </div>
        </div>

         <p className={`mt-5 text-center text-2xs ${THEME_COLORS.textDark}`}> {/* Compacted mt-6, text-xs */}
            By authenticating, you agree to our <Link to={ROUTES.TERMS} className="underline hover:text-medium">Service Protocol</Link> and <Link to={ROUTES.PRIVACY} className="underline hover:text-medium">Privacy Measures</Link>.
        </p>
      </div>
    </div>
  );
};

export default LoginPage;
