
import React, { useState } from 'react';
import { THEME_COLORS } from '../constants';
import { MapPinIcon } from '../components/icons/MapPinIcon';
import { CalendarDaysIcon } from '../components/icons/CalendarDaysIcon';
import { UserGroupIcon } from '../components/icons/UserGroupIcon';
import { CurrencyDollarIcon } from '../components/icons/CurrencyDollarIcon';
import { PetIcon } from '../components/icons/PetIcon';
import { WalletIcon } from '../components/icons/WalletIcon'; // Keep this one

const FormField: React.FC<{ label: string; name: string; type?: string; placeholder: string; value: string | number; onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void; icon?: React.ReactNode; as?: 'textarea' | 'select'; options?: {value: string; label: string}[] }> = 
  ({ label, name, type = "text", placeholder, value, onChange, icon, as, options }) => (
  <div>
    <label htmlFor={name} className={`block text-xs font-medium ${THEME_COLORS.textPrimaryAccent} mb-0.5`}>{label}</label> {/* Compacted text-sm, mb-1 */}
    <div className="relative">
      {icon && !as && <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">{icon}</div>} {/* Compacted pl-3 */}
      {as === 'textarea' ? (
        <textarea
          id={name}
          name={name}
          rows={2} // Compacted rows
          placeholder={placeholder}
          value={value as string}
          onChange={onChange}
          className={`w-full p-2.5 text-sm ${THEME_COLORS.bgGlass} rounded-lg border ${THEME_COLORS.borderPrimaryAccent}/50 focus:ring-2 ${THEME_COLORS.ringPrimaryAccent} focus:${THEME_COLORS.borderPrimaryAccent} outline-none transition-all ${THEME_COLORS.textLight} placeholder-[var(--color-text-dark)]`} // Compacted p-3
        />
      ) : as === 'select' ? (
        <select
          id={name}
          name={name}
          value={value}
          onChange={onChange}
          className={`w-full p-2.5 text-sm ${icon ? 'pl-9' : ''} ${THEME_COLORS.bgGlass} rounded-lg border ${THEME_COLORS.borderPrimaryAccent}/50 focus:ring-2 ${THEME_COLORS.ringPrimaryAccent} focus:${THEME_COLORS.borderPrimaryAccent} outline-none transition-all ${THEME_COLORS.textLight} placeholder-[var(--color-text-dark)] appearance-none bg-transparent`}
        >
          {options?.map(opt => <option key={opt.value} value={opt.value} className="bg-[var(--color-dark-bg)]">{opt.label}</option>)}
        </select>
      ) : (
        <input
          type={type}
          id={name}
          name={name}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          className={`w-full p-2.5 text-sm ${icon ? 'pl-9' : ''} ${THEME_COLORS.bgGlass} rounded-lg border ${THEME_COLORS.borderPrimaryAccent}/50 focus:ring-2 ${THEME_COLORS.ringPrimaryAccent} focus:${THEME_COLORS.borderPrimaryAccent} outline-none transition-all ${THEME_COLORS.textLight} placeholder-[var(--color-text-dark)]`}
        />
      )}
    </div>
  </div>
);


const ListLegPage: React.FC = () => {
  const [formData, setFormData] = useState({
    aircraftMakeModel: '',
    departureAirport: '',
    arrivalAirport: '',
    departureDateTime: '',
    availableSeats: '',
    pricePerSeatUSD: '',
    pricePerSeatETH: '',
    petAllowance: 'false',
    description: '',
  });
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('Flight Listing Data:', formData);
    setIsSubmitted(true);
    setTimeout(() => {
        setIsSubmitted(false);
        // Optionally reset form
    }, 5000); 
  };

  return (
    <div className="max-w-xl mx-auto space-y-10"> {/* Compacted max-w-2xl, space-y-12 */}
      <h1 className={`text-3xl font-bold text-center ${THEME_COLORS.gradientTextUser} mb-6`}>Manifest Your Empty Leg</h1> {/* Compacted text-4xl, mb-8 */}
      
      {isSubmitted && (
        <div className={`p-3 mb-5 rounded-md bg-[var(--color-primary-purple)]/15 border ${THEME_COLORS.borderPrimaryAccent} ${THEME_COLORS.textPrimaryAccent} text-center shadow-lg ${THEME_COLORS.shadowPrimary} text-sm`}> {/* Compacted p-4, mb-6 */}
          Flight vector submitted to the mempool! Awaiting on-chain confirmation...
        </div>
      )}

      <form onSubmit={handleSubmit} className={`${THEME_COLORS.bgGlass} p-5 md:p-6 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/20 space-y-5 scroll-animation`}> {/* Compacted p-6/8, space-y-6 */}
        <FormField label="Aircraft Model Designation" name="aircraftMakeModel" placeholder="e.g., Void Runner G700 Custom" value={formData.aircraftMakeModel} onChange={handleChange} />
        
        <div className="grid md:grid-cols-2 gap-4"> {/* Compacted gap-6 */}
          <FormField label="Departure Node (ICAO)" name="departureAirport" placeholder="e.g., KTEB" value={formData.departureAirport} onChange={handleChange} icon={<MapPinIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
          <FormField label="Arrival Node (ICAO)" name="arrivalAirport" placeholder="e.g., KVNY" value={formData.arrivalAirport} onChange={handleChange} icon={<MapPinIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
        </div>
        
        <FormField label="Departure Date & Time (UTC)" name="departureDateTime" type="datetime-local" placeholder="" value={formData.departureDateTime} onChange={handleChange} icon={<CalendarDaysIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
        
        <div className="grid md:grid-cols-2 gap-4">
          <FormField label="Available Seats" name="availableSeats" type="number" placeholder="e.g., 4" value={formData.availableSeats} onChange={handleChange} icon={<UserGroupIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
          <FormField 
            label="Pet Protocol" 
            name="petAllowance" 
            value={formData.petAllowance} 
            onChange={handleChange} 
            as="select" 
            options={[{value: 'false', label: 'No Pets (Standard Ops)'}, {value: 'true', label: 'Pets Welcome (Augmented Cabin)'}]}
            placeholder=""
          />
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          <FormField label="Price/Seat (USD)" name="pricePerSeatUSD" type="number" placeholder="e.g., 2500" value={formData.pricePerSeatUSD} onChange={handleChange} icon={<CurrencyDollarIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
          <FormField label="Price/Seat (ETH - Optional)" name="pricePerSeatETH" type="number" placeholder="e.g., 0.75" value={formData.pricePerSeatETH} onChange={handleChange} icon={<WalletIcon className={`w-4 h-4 ${THEME_COLORS.textMedium}`}/>} />
        </div>
        
        <FormField label="Flight Vector Notes / Alpha" name="description" as="textarea" placeholder="e.g., Quantum Wi-Fi, zero-G capable, bespoke amenities..." value={formData.description} onChange={handleChange} />
        
        <div className={`border-t ${THEME_COLORS.borderPrimaryAccent}/30 pt-5 mt-5`}> {/* Compacted pt-6, mt-6 */}
            <h3 className={`text-lg font-semibold mb-2.5 ${THEME_COLORS.textSecondaryAccent}`}>Listing Preview</h3> {/* Compacted text-xl, mb-3 */}
            <div className={`p-3 bg-[var(--color-dark-bg)]/60 rounded-lg space-y-0.5 text-xs border ${THEME_COLORS.borderPrimaryAccent}/30`}> {/* Compacted p-4, text-sm, space-y-1 */}
                <p><strong className={THEME_COLORS.textMedium}>Aircraft:</strong> {formData.aircraftMakeModel || "N/A"}</p>
                <p><strong className={THEME_COLORS.textMedium}>Route:</strong> {formData.departureAirport || "N/A"} to {formData.arrivalAirport || "N/A"}</p>
                <p><strong className={THEME_COLORS.textMedium}>Departure:</strong> {formData.departureDateTime ? new Date(formData.departureDateTime).toLocaleString() : "N/A"}</p>
                <p><strong className={THEME_COLORS.textMedium}>Seats:</strong> {formData.availableSeats || "N/A"}, Pets: {formData.petAllowance === 'true' ? 'Welcome' : 'Standard Ops'}</p>
                <p><strong className={THEME_COLORS.textMedium}>Price:</strong> ${formData.pricePerSeatUSD || "0"} {formData.pricePerSeatETH ? `/ ${formData.pricePerSeatETH} ETH` : ""}</p>
            </div>
        </div>

        <button 
          type="submit"
          disabled={isSubmitted}
          className={`w-full ${THEME_COLORS.gradientBgButton} text-white font-bold py-2.5 px-5 rounded-lg text-base shadow-lg hover:shadow-xl ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} ${isSubmitted ? 'opacity-50 cursor-not-allowed' : ''}`} // Compacted py-3, px-6, text-lg
        >
          {isSubmitted ? 'Broadcasting to Network...' : 'Commit Flight to Ledger'}
        </button>
      </form>
    </div>
  );
};

export default ListLegPage;
