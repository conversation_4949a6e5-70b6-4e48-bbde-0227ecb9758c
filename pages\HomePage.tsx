
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ROUTES, THEME_COLORS, MOCK_FLIGHTS, NAV_LINKS_CONFIG } from '../constants';
import FlightCard from '../components/FlightCard';
import { useWallet } from '../contexts/WalletContext';
import { SparklesIcon } from '../components/icons/SparklesIcon';
import { PetIcon } from '../components/icons/PetIcon';
import { ShieldCheckIcon } from '../components/icons/ShieldCheckIcon';
import { WalletIcon } from '../components/icons/WalletIcon';
import { PlaneIcon } from '../components/icons/PlaneIcon';
import HeroBackgroundAnimation from '../components/HeroBackgroundAnimation'; // New

const HomePage: React.FC = () => {
  const { connectWallet, isConnected } = useWallet();
  const featuredFlights = MOCK_FLIGHTS.slice(0, 3);

  const howItWorksSteps = [
    {
      title: 'Sync & Discover',
      description: 'Pilots: Manifest your empty legs. Travelers: Uncover unique flight vectors. Secure wallet sync for Web3 enhancements.',
      icon: <WalletIcon className={`w-8 h-8 ${THEME_COLORS.textPrimaryAccent}`} />, // Compacted w-10 h-10
      numberColor: THEME_COLORS.textPrimaryAccent,
    },
    {
      title: 'Verify & Transact',
      description: 'Secure seats with crypto or fiat. Our platform integrates on-chain elements for unparalleled transparency and trust.',
      icon: <ShieldCheckIcon className={`w-8 h-8 ${THEME_COLORS.textSecondaryAccent}`} />,
      numberColor: THEME_COLORS.textSecondaryAccent,
    },
    {
      title: 'Fly Beyond',
      description: 'Experience the next-gen private travel. Pet-friendly options, amplified privacy, and a community-driven flight ecosystem.',
      icon: <PlaneIcon className={`w-8 h-8 ${THEME_COLORS.gradientTextUser}`} />, // Use gradient for this one
      numberColor: THEME_COLORS.gradientTextUser,
    },
  ];

  return (
    <div className="space-y-12 md:space-y-20"> {/* Compacted space-y-16/24 */}
      {/* Hero Section */}
      <section className="text-center py-16 md:py-24 rounded-xl shadow-2xl shadow-black/60 -mt-6 -mx-4 md:-mt-10 md:-mx-6 relative hero-section-container overflow-hidden min-h-[70vh] md:min-h-[60vh] flex flex-col justify-center items-center"> {/* Compacted py, mt, mx */}
        <HeroBackgroundAnimation />
        <div className="absolute inset-0 bg-[var(--color-dark-bg)]/70 backdrop-blur-xs"></div> {/* Overlay for text readability over animation */}
        <div className="relative z-10 container mx-auto px-4">
          <h1 className={`text-4xl sm:text-5xl md:text-5xl font-bold mb-5 ${THEME_COLORS.gradientTextUser} tracking-wide leading-tight`}> {/* Compacted text-6xl, mb-6 */}
            Redefine Your Skyline. <br className="hidden md:block" /> Private Aviation, Evolved.
          </h1>
          <p className={`text-base md:text-lg ${THEME_COLORS.textMedium} mb-8 max-w-2xl mx-auto`}> {/* Compacted text-lg/xl, mb-10 */}
            JetSharing.co: Direct pilot connections for empty leg flights. Crypto payments, pet-friendly journeys, the future of air mobility is here.
          </p>
          <div className="space-y-3 sm:space-y-0 sm:space-x-4"> {/* Compacted space-y-4, space-x-6 */}
            <Link
              to={ROUTES.SEARCH_FLIGHTS}
              className={`inline-block ${THEME_COLORS.gradientBgButton} text-white font-bold py-3 px-8 rounded-lg text-base shadow-lg hover:shadow-xl ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} transform hover:scale-105`} // Compacted py-4, px-10, text-lg
            >
              {NAV_LINKS_CONFIG.findFlights}
            </Link>
            <Link
              to={ROUTES.LIST_LEG}
              className={`inline-block bg-transparent hover:bg-[var(--color-primary-purple)]/20 ${THEME_COLORS.textPrimaryAccent} font-bold py-3 px-8 rounded-lg text-base border-2 ${THEME_COLORS.borderPrimaryAccent} shadow-md hover:shadow-lg ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} transform hover:scale-105`}
            >
              {NAV_LINKS_CONFIG.listLeg}
            </Link>
            {!isConnected && (
              <button
                onClick={connectWallet}
                className={`inline-block bg-[var(--color-glass-bg)] hover:border-[var(--color-secondary-red)] ${THEME_COLORS.textSecondaryAccent} font-bold py-3 px-8 rounded-lg text-base border-2 ${THEME_COLORS.borderSecondaryAccent}/70 shadow-lg hover:shadow-xl ${THEME_COLORS.shadowSecondary} transition-all duration-300 ${THEME_COLORS.buttonGlowSecondary} transform hover:scale-105 mt-3 sm:mt-0`}
              >
                {NAV_LINKS_CONFIG.connectWallet}
              </button>
            )}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="scroll-animation">
        <h2 className={`text-2xl md:text-3xl font-bold text-center mb-10 ${THEME_COLORS.gradientTextUser}`}>Why JetSharing.co?</h2> {/* Compacted text-3xl/4xl, mb-12 */}
        <div className="grid md:grid-cols-3 gap-6 text-center"> {/* Compacted gap-8 */}
          <div className={`${THEME_COLORS.bgGlass} p-6 rounded-xl shadow-lg hover:${THEME_COLORS.shadowPrimary}/40 transition-shadow duration-300`}> {/* Compacted p-8 */}
            <SparklesIcon className={`w-12 h-12 mx-auto mb-3 ${THEME_COLORS.textPrimaryAccent}`} /> {/* Compacted w-16 h-16, mb-4 */}
            <h3 className={`text-xl font-semibold mb-1.5 ${THEME_COLORS.textLight}`}>Web3 Integrated</h3> {/* Compacted text-2xl, mb-2 */}
            <p className={`${THEME_COLORS.textMedium} text-xs`}>Crypto payments, on-chain potential, future-proof platform.</p> {/* Compacted text size */}
          </div>
          <div className={`${THEME_COLORS.bgGlass} p-6 rounded-xl shadow-lg hover:${THEME_COLORS.shadowSecondary}/40 transition-shadow duration-300`}>
            <PetIcon className={`w-12 h-12 mx-auto mb-3 ${THEME_COLORS.textSecondaryAccent}`} />
            <h3 className={`text-xl font-semibold mb-1.5 ${THEME_COLORS.textLight}`}>Pet Cyber-Companions</h3>
            <p className={`${THEME_COLORS.textMedium} text-xs`}>Many flights welcome all companions. Travel without archaic restrictions.</p>
          </div>
          <div className={`${THEME_COLORS.bgGlass} p-6 rounded-xl shadow-lg hover:${THEME_COLORS.shadowPrimary}/40 transition-shadow duration-300`}>
            <ShieldCheckIcon className={`w-12 h-12 mx-auto mb-3 ${THEME_COLORS.textPrimaryAccent}`} />
            <h3 className={`text-xl font-semibold mb-1.5 ${THEME_COLORS.textLight}`}>Verified & Transparent</h3>
            <p className={`${THEME_COLORS.textMedium} text-xs`}>Focus on pilot verification & transparent flight data. Trust through cryptography.</p>
          </div>
        </div>
      </section>

      {/* Featured Flights Section */}
      <section className="scroll-animation">
        <h2 className={`text-2xl md:text-3xl font-bold text-center mb-10 ${THEME_COLORS.textSecondaryAccent}`}>Featured Flight Vectors</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredFlights.map(flight => (
            <FlightCard key={flight.id} flight={flight} />
          ))}
        </div>
        <div className="text-center mt-10"> {/* Compacted mt-12 */}
          <Link
            to={ROUTES.SEARCH_FLIGHTS}
            className={`inline-block ${THEME_COLORS.gradientBgButton} text-white font-semibold py-2.5 px-6 rounded-lg shadow-md hover:shadow-lg ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} text-sm`} // Compacted py-3, px-8
          >
            Explore All Flight Paths
          </Link>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="scroll-animation">
        <h2 className={`text-2xl md:text-3xl font-bold text-center mb-10 ${THEME_COLORS.gradientTextUser}`}>Streamlined Protocol</h2>
        <div className="grid md:grid-cols-3 gap-6">
          {howItWorksSteps.map((step, index) => (
            <div key={index} className={`${THEME_COLORS.bgGlass} p-6 rounded-xl shadow-lg hover:${index % 2 === 0 ? THEME_COLORS.shadowPrimary : THEME_COLORS.shadowSecondary}/30 transition-shadow duration-300 flex flex-col items-center text-center`}>
              <div className="flex items-center justify-center mb-4"> {/* Compacted mb-6 */}
                <span className={`text-4xl font-bold mr-3 ${step.numberColor}`}>0{index + 1}</span> {/* Compacted text-5xl, mr-4 */}
                {step.icon}
              </div>
              <h3 className={`text-xl font-semibold mb-2 ${step.numberColor}`}>{step.title}</h3> {/* Compacted text-2xl, mb-3 */}
              <p className={`${THEME_COLORS.textMedium} text-xs`}>{step.description}</p> {/* Compacted text-sm */}
            </div>
          ))}
        </div>
      </section>

      {/* Call to Action - Membership */}
      <section className={`${THEME_COLORS.bgGlass} rounded-xl p-6 md:p-10 text-center shadow-xl ${THEME_COLORS.shadowPrimary}/20 scroll-animation`}> {/* Compacted p-8/12 */}
        <h2 className={`text-2xl md:text-3xl font-bold mb-4 ${THEME_COLORS.gradientTextUser}`}>Unlock Hyperdrive Features</h2> {/* Compacted text-3xl/4xl, mb-6 */}
        <p className={`text-base ${THEME_COLORS.textMedium} mb-6 max-w-xl mx-auto`}> {/* Compacted text-lg, mb-8, max-w-2xl */}
          Ascend through JetSharing.co membership tiers. Access advanced matrix filters, priority notifications, and exclusive flight vectors.
        </p>
        <Link
          to={ROUTES.MEMBERSHIP}
          className={`inline-block ${THEME_COLORS.gradientBgButton} text-white font-bold py-3 px-8 rounded-lg text-base shadow-lg hover:shadow-xl ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} transform hover:scale-105`}
        >
          Explore Membership Tiers
        </Link>
      </section>
    </div>
  );
};

export default HomePage;
