
import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ROUTES, THEME_COLORS, MOCK_FLIGHTS, NAV_LINKS_CONFIG } from '../constants';
import FlightCard from '../components/FlightCard';
import { useWallet } from '../contexts/WalletContext';
import { SparklesIcon } from '../components/icons/SparklesIcon';
import { PetIcon } from '../components/icons/PetIcon';
import { ShieldCheckIcon } from '../components/icons/ShieldCheckIcon';
import { WalletIcon } from '../components/icons/WalletIcon';
import { PlaneIcon } from '../components/icons/PlaneIcon';
import HeroBackgroundAnimation from '../components/HeroBackgroundAnimation';
import AnimatedButton from '../components/AnimatedButton';
import { Rocket, Zap, Shield, Sparkles, Plane, Users, Star, ArrowRight } from 'lucide-react';

const HomePage: React.FC = () => {
  const { connectWallet, isConnected } = useWallet();
  const featuredFlights = MOCK_FLIGHTS.slice(0, 3);

  const howItWorksSteps = [
    {
      title: 'Sync & Discover',
      description: 'Pilots: Manifest your empty legs. Travelers: Uncover unique flight vectors. Secure wallet sync for Web3 enhancements.',
      icon: <Rocket className="w-8 h-8" />,
      color: 'from-purple-500 to-pink-500',
      glowColor: 'rgba(157, 0, 255, 0.5)',
    },
    {
      title: 'Verify & Transact',
      description: 'Secure seats with crypto or fiat. Our platform integrates on-chain elements for unparalleled transparency and trust.',
      icon: <Shield className="w-8 h-8" />,
      color: 'from-red-500 to-orange-500',
      glowColor: 'rgba(255, 0, 60, 0.5)',
    },
    {
      title: 'Fly Beyond',
      description: 'Experience the next-gen private travel. Pet-friendly options, amplified privacy, and a community-driven flight ecosystem.',
      icon: <Plane className="w-8 h-8" />,
      color: 'from-cyan-500 to-blue-500',
      glowColor: 'rgba(0, 255, 255, 0.5)',
    },
  ];

  const benefits = [
    {
      title: 'Web3 Integrated',
      description: 'Crypto payments, on-chain potential, future-proof platform.',
      icon: <Zap className="w-12 h-12" />,
      color: 'from-purple-500 to-pink-500',
    },
    {
      title: 'Pet Cyber-Companions',
      description: 'Many flights welcome all companions. Travel without archaic restrictions.',
      icon: <Sparkles className="w-12 h-12" />,
      color: 'from-pink-500 to-red-500',
    },
    {
      title: 'Verified & Transparent',
      description: 'Focus on pilot verification & transparent flight data. Trust through cryptography.',
      icon: <Shield className="w-12 h-12" />,
      color: 'from-cyan-500 to-purple-500',
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 10,
      },
    },
  };

  return (
    <div className="space-y-20 md:space-y-32">
      {/* Enhanced Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex flex-col justify-center items-center -mt-6 -mx-4 md:-mt-10 md:-mx-6">
        {/* Enhanced Background Animation */}
        <div className="absolute inset-0">
          <HeroBackgroundAnimation />
        </div>

        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/30 to-black/50" />

        {/* Floating elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-purple-400 rounded-full opacity-30"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0.3, 1, 0.3],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 4 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 4,
              }}
            />
          ))}
        </div>

        <motion.div
          className="relative z-10 container mx-auto px-4 text-center"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <motion.h1
            className="text-5xl sm:text-6xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent leading-tight"
            variants={itemVariants}
          >
            Redefine Your Skyline
            <br />
            <span className="text-gradient-holographic">Private Aviation, Evolved</span>
          </motion.h1>

          <motion.p
            className="text-lg md:text-xl text-gray-300 mb-10 max-w-3xl mx-auto leading-relaxed"
            variants={itemVariants}
          >
            JetSharing.co: Direct pilot connections for empty leg flights. Crypto payments,
            pet-friendly journeys, the future of air mobility is here.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            variants={itemVariants}
          >
            <AnimatedButton
              variant="holographic"
              size="xl"
              icon={<Plane className="w-5 h-5" />}
              iconPosition="right"
              onClick={() => window.location.href = `#${ROUTES.SEARCH_FLIGHTS}`}
            >
              {NAV_LINKS_CONFIG.findFlights}
            </AnimatedButton>

            <AnimatedButton
              variant="secondary"
              size="xl"
              icon={<Rocket className="w-5 h-5" />}
              iconPosition="right"
              onClick={() => window.location.href = `#${ROUTES.LIST_LEG}`}
            >
              {NAV_LINKS_CONFIG.listLeg}
            </AnimatedButton>

            {!isConnected && (
              <AnimatedButton
                variant="ghost"
                size="xl"
                icon={<Zap className="w-5 h-5" />}
                iconPosition="left"
                onClick={connectWallet}
              >
                {NAV_LINKS_CONFIG.connectWallet}
              </AnimatedButton>
            )}
          </motion.div>
        </motion.div>
      </section>

      {/* Enhanced Benefits Section */}
      <motion.section
        className="scroll-animation container mx-auto px-4"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        <motion.h2
          className="text-4xl md:text-5xl font-bold text-center mb-16 text-gradient-holographic"
          variants={itemVariants}
        >
          Why JetSharing.co?
        </motion.h2>

        <div className="grid md:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              className="group relative"
              variants={itemVariants}
              whileHover={{ y: -10 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              <div className="card-enhanced p-8 text-center h-full relative overflow-hidden">
                {/* Animated background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${benefit.color} opacity-5 group-hover:opacity-10 transition-opacity duration-500`} />

                {/* Floating icon */}
                <motion.div
                  className={`w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-br ${benefit.color} p-4 text-white relative z-10`}
                  whileHover={{
                    rotate: 360,
                    scale: 1.1,
                  }}
                  transition={{ duration: 0.6 }}
                >
                  {benefit.icon}
                </motion.div>

                <h3 className="text-2xl font-bold mb-4 text-white relative z-10">
                  {benefit.title}
                </h3>

                <p className="text-gray-300 leading-relaxed relative z-10">
                  {benefit.description}
                </p>

                {/* Hover effect particles */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                  {[...Array(5)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-white rounded-full"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                      }}
                      animate={{
                        y: [0, -20, 0],
                        opacity: [0, 1, 0],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: Math.random() * 2,
                      }}
                    />
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Enhanced Featured Flights Section */}
      <motion.section
        className="scroll-animation container mx-auto px-4"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={containerVariants}
      >
        <motion.h2
          className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-red-400 via-pink-400 to-purple-400 bg-clip-text text-transparent"
          variants={itemVariants}
        >
          Featured Flight Vectors
        </motion.h2>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
        >
          {featuredFlights.map((flight, index) => (
            <motion.div
              key={flight.id}
              variants={itemVariants}
              custom={index}
            >
              <FlightCard flight={flight} />
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="text-center mt-16"
          variants={itemVariants}
        >
          <AnimatedButton
            variant="primary"
            size="lg"
            icon={<ArrowRight className="w-5 h-5" />}
            iconPosition="right"
            onClick={() => window.location.href = `#${ROUTES.SEARCH_FLIGHTS}`}
          >
            Explore All Flight Paths
          </AnimatedButton>
        </motion.div>
      </motion.section>

      {/* Enhanced How It Works Section */}
      <motion.section
        className="scroll-animation container mx-auto px-4"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        <motion.h2
          className="text-4xl md:text-5xl font-bold text-center mb-16 text-gradient-holographic"
          variants={itemVariants}
        >
          Streamlined Protocol
        </motion.h2>

        <div className="grid md:grid-cols-3 gap-8">
          {howItWorksSteps.map((step, index) => (
            <motion.div
              key={index}
              className="group relative"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              <div className="card-enhanced p-8 text-center h-full relative overflow-hidden">
                {/* Step number with glow */}
                <motion.div
                  className="absolute -top-4 -right-4 w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-2xl font-bold text-white shadow-lg"
                  whileHover={{
                    rotate: 360,
                    scale: 1.1,
                  }}
                  transition={{ duration: 0.6 }}
                  style={{
                    boxShadow: `0 0 20px ${step.glowColor}`,
                  }}
                >
                  {index + 1}
                </motion.div>

                {/* Animated icon */}
                <motion.div
                  className={`w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br ${step.color} p-5 text-white relative z-10`}
                  whileHover={{
                    y: -10,
                    rotate: [0, -10, 10, 0],
                  }}
                  transition={{ duration: 0.6 }}
                >
                  {step.icon}
                </motion.div>

                <h3 className={`text-2xl font-bold mb-4 bg-gradient-to-r ${step.color} bg-clip-text text-transparent`}>
                  {step.title}
                </h3>

                <p className="text-gray-300 leading-relaxed">
                  {step.description}
                </p>

                {/* Connection line to next step */}
                {index < howItWorksSteps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-purple-500 to-transparent transform -translate-y-1/2" />
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Enhanced Call to Action - Membership */}
      <motion.section
        className="scroll-animation container mx-auto px-4"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={containerVariants}
      >
        <motion.div
          className="relative overflow-hidden rounded-2xl p-12 text-center"
          style={{
            background: 'linear-gradient(135deg, rgba(157, 0, 255, 0.1) 0%, rgba(255, 0, 60, 0.1) 100%)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(157, 0, 255, 0.3)',
          }}
          variants={itemVariants}
          whileHover={{ scale: 1.02 }}
          transition={{ type: 'spring', stiffness: 300 }}
        >
          {/* Animated background elements */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(10)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-purple-400 rounded-full opacity-20"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -50, 0],
                  opacity: [0.2, 0.8, 0.2],
                  scale: [1, 1.5, 1],
                }}
                transition={{
                  duration: 3 + Math.random() * 3,
                  repeat: Infinity,
                  delay: Math.random() * 3,
                }}
              />
            ))}
          </div>

          <motion.h2
            className="text-4xl md:text-5xl font-bold mb-6 text-gradient-holographic relative z-10"
            variants={itemVariants}
          >
            Unlock Hyperdrive Features
          </motion.h2>

          <motion.p
            className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed relative z-10"
            variants={itemVariants}
          >
            Ascend through JetSharing.co membership tiers. Access advanced matrix filters,
            priority notifications, and exclusive flight vectors in the Web3 aviation ecosystem.
          </motion.p>

          <motion.div variants={itemVariants} className="relative z-10">
            <AnimatedButton
              variant="holographic"
              size="xl"
              icon={<Star className="w-5 h-5" />}
              iconPosition="right"
              onClick={() => window.location.href = `#${ROUTES.MEMBERSHIP}`}
            >
              Explore Membership Tiers
            </AnimatedButton>
          </motion.div>
        </motion.div>
      </motion.section>
    </div>
  );
};

export default HomePage;
