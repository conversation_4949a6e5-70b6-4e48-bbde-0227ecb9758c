
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { UserProfile, Flight, UserRole, BadgeType } from '../types';
import { MOCK_USERS, MOCK_FLIGHTS, THEME_COLORS, ROUTES } from '../constants';
import FlightCard from '../components/FlightCard';
import { SparklesIcon } from '../components/icons/SparklesIcon';
import { PetIcon } from '../components/icons/PetIcon';
import { WalletIcon } from '../components/icons/WalletIcon';


const BadgeDisplayItem: React.FC<{ badge: BadgeType }> = ({ badge }) => {
  let colorClasses = `${THEME_COLORS.bgDark} ${THEME_COLORS.textMedium} border-[var(--color-text-dark)]/50`;
  let icon: React.ReactNode = null;

  switch (badge) {
    case BadgeType.VerifiedPilot:
      colorClasses = `bg-[var(--color-primary-purple)]/15 ${THEME_COLORS.textPrimaryAccent} ${THEME_COLORS.borderPrimaryAccent}/50`;
      icon = <SparklesIcon className="w-3 h-3 mr-1" />; // Compacted
      break;
    case BadgeType.CryptoNative:
      colorClasses = `bg-[var(--color-secondary-red)]/15 ${THEME_COLORS.textSecondaryAccent} ${THEME_COLORS.borderSecondaryAccent}/50`;
      icon = <WalletIcon className="w-3 h-3 mr-1" />; // Compacted
      break;
    case BadgeType.PetFriendlyHost:
      colorClasses = `bg-[var(--color-primary-purple)]/20 text-[var(--color-gradient-pink)] ${THEME_COLORS.borderPrimaryAccent}/40`; // Pinker purple
      icon = <PetIcon className="w-3 h-3 mr-1" />; // Compacted
      break;
    case BadgeType.TopRated: 
      colorClasses = `bg-transparent ${THEME_COLORS.gradientTextUser} ${THEME_COLORS.borderPrimaryAccent}/60`; // Gradient text
      icon = <SparklesIcon className="w-3 h-3 mr-1" />; // Compacted
      break;
    case BadgeType.EarlyAdopter:
      colorClasses = `bg-[var(--color-text-dark)]/20 ${THEME_COLORS.textMedium} border-[var(--color-text-dark)]/40`;
      break;
  }

  return (
    <span className={`text-2xs font-medium px-2.5 py-1 rounded-full inline-flex items-center shadow-sm border ${colorClasses}`}> {/* Compacted text-xs, px-3, py-1.5 */}
      {icon}
      {badge}
    </span>
  );
};


const UserProfilePage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const [user, setUser] = useState<UserProfile | null>(null);
  const [userFlights, setUserFlights] = useState<Flight[]>([]);

  useEffect(() => {
    const foundUser = MOCK_USERS.find(u => u.id === userId);
    setUser(foundUser || null);
    if (foundUser && foundUser.role === UserRole.Pilot) {
      setUserFlights(MOCK_FLIGHTS.filter(f => f.pilotId === foundUser.id));
    }
  }, [userId]);

  if (!user) {
    return <div className={`text-center py-16 text-xl ${THEME_COLORS.textPrimaryAccent}`}>User datastream not found. Recalibrating...</div>; // Compacted py-20, text-2xl
  }

  return (
    <div className="space-y-10"> {/* Compacted space-y-12 */}
      <section className={`${THEME_COLORS.bgGlass} p-5 md:p-6 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/20 scroll-animation`}> {/* Compacted p-6/8 */}
        <div className="flex flex-col md:flex-row items-center md:items-start gap-6"> {/* Compacted gap-8 */}
          <img
            src={user.profilePictureUrl || `https://picsum.photos/seed/${user.id}/120/120`} // Compacted 150 to 120
            alt={`${user.name} - Profile Avatar`}
            className={`w-28 h-28 md:w-40 md:h-40 rounded-full border-4 ${THEME_COLORS.borderPrimaryAccent} object-cover shadow-lg`} // Compacted w-32/48, h-32/48
          />
          <div className="flex-1 text-center md:text-left">
            <h1 className={`text-3xl font-bold ${THEME_COLORS.gradientTextUser} mb-1.5`}>{user.name}</h1> {/* Compacted text-4xl, mb-2 */}
            <p className={`text-lg ${THEME_COLORS.textLight} mb-0.5`}> {/* Compacted text-xl, mb-1 */}
              {user.role === UserRole.Pilot ? `Pilot (${user.pilotRating} ★)` : 'Traveler'}
            </p>
            {user.location && <p className={`${THEME_COLORS.textMedium} text-xs mb-2`}>{user.location}</p>} {/* Compacted mb-3 */}
            {user.walletAddress && (
              <p className={`text-xs ${THEME_COLORS.textMedium} mb-0.5 flex items-center justify-center md:justify-start`}> {/* Compacted mb-1 */}
                <WalletIcon className={`w-3.5 h-3.5 mr-1.5 ${THEME_COLORS.textSecondaryAccent}`} /> {user.walletAddress} (Chain ID) {/* Compacted w-4 h-4, mr-2 */}
              </p>
            )}
            <p className={`text-2xs ${THEME_COLORS.textDark}`}>Member Since: {new Date(user.memberSince).toLocaleDateString()}</p> {/* Compacted text-xs */}
            
            <div className="mt-3 flex flex-wrap gap-1.5 justify-center md:justify-start"> {/* Compacted mt-4, gap-2 */}
              {user.badges.map(badge => <BadgeDisplayItem key={badge} badge={badge} />)}
            </div>
          </div>
        </div>
        {user.bio && <p className={`mt-5 ${THEME_COLORS.textLight} text-sm text-center md:text-left`}>{user.bio}</p>} {/* Compacted mt-6 */}
      </section>

      {user.role === UserRole.Pilot && user.aircraftDetails && user.aircraftDetails.length > 0 && (
        <section className={`${THEME_COLORS.bgGlass} p-5 rounded-xl shadow-lg ${THEME_COLORS.shadowPrimary}/10 scroll-animation`}> {/* Compacted p-6 */}
          <h2 className={`text-xl font-bold mb-3 ${THEME_COLORS.textSecondaryAccent}`}>Aircraft Fleet</h2> {/* Compacted text-2xl, mb-4 */}
          {user.aircraftDetails.map((craft, index) => (
            <div key={index} className="mb-1.5 p-2.5 bg-[var(--color-dark-bg)]/50 rounded-md border border-[var(--color-glass-border)]/70"> {/* Compacted mb-2, p-3 */}
              <p className={`font-semibold ${THEME_COLORS.textLight} text-sm`}>{craft.make} {craft.model}</p>
              <p className={`text-2xs ${THEME_COLORS.textDark}`}>Registry: {craft.registration}</p> {/* Compacted text-xs */}
            </div>
          ))}
          {user.certifications && user.certifications.length > 0 && (
             <div className="mt-3"> {/* Compacted mt-4 */}
                <h3 className={`text-base font-semibold mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>Credentials</h3> {/* Compacted text-lg, mb-2 */}
                <ul className={`list-disc list-inside ${THEME_COLORS.textMedium} text-xs space-y-0.5`}> {/* Compacted text-sm, space-y-1 */}
                    {user.certifications.map((cert, idx) => <li key={idx}>{cert}</li>)}
                </ul>
             </div>
          )}
        </section>
      )}

      {user.role === UserRole.Traveler && user.preferences && user.preferences.length > 0 && (
        <section className={`${THEME_COLORS.bgGlass} p-5 rounded-xl shadow-lg ${THEME_COLORS.shadowPrimary}/10 scroll-animation`}>
          <h2 className={`text-xl font-bold mb-3 ${THEME_COLORS.textSecondaryAccent}`}>Traveler Preferences</h2>
          <ul className="flex flex-wrap gap-1.5"> {/* Compacted gap-2 */}
            {user.preferences.map((pref, index) => {
              const baseClasses = "bg-[var(--color-secondary-red)]/20 px-2.5 py-1 rounded-full text-xs border";
              const colorClasses = `${THEME_COLORS.textSecondaryAccent} ${THEME_COLORS.borderSecondaryAccent}/50`;
              return (
                <li key={index} className={`${baseClasses} ${colorClasses}`}>{pref}</li>
              );
            })}
          </ul>
        </section>
      )}
      
      {user.role === UserRole.Pilot && (
        <section className="scroll-animation">
          <h2 className={`text-xl font-bold mb-5 ${THEME_COLORS.textSecondaryAccent}`}>Flight Manifests by {user.name.split(" ")[0]}</h2> {/* Compacted text-2xl, mb-6 */}
          {userFlights.length > 0 ? (
            <div className="grid md:grid-cols-2 gap-6"> {/* Compacted gap-8 */}
              {userFlights.map(flight => <FlightCard key={flight.id} flight={flight} />)}
            </div>
          ) : (
            <p className={`${THEME_COLORS.textMedium} text-sm`}>This pilot has no active flight manifests.</p>
          )}
        </section>
      )}

      {user.role === UserRole.Traveler && (
        <section className="scroll-animation">
           <h2 className={`text-xl font-bold mb-5 ${THEME_COLORS.textSecondaryAccent}`}>Traveler Logs</h2>
           <p className={`${THEME_COLORS.textMedium} text-sm`}>Past flight logs and saved search vectors will materialize here. (Data Stream Pending)</p>
        </section>
      )}
    </div>
  );
};

export default UserProfilePage;
