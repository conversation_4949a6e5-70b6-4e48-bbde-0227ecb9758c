
import React from 'react';
import { THEME_COLORS } from '../constants';

const FaaDisclaimerPage: React.FC = () => {
  return (
    <div className={`max-w-2xl mx-auto ${THEME_COLORS.bgGlass} p-5 md:p-8 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/20 scroll-animation`}> {/* Compacted max-w-3xl, p-6/10 */}
      <h1 className={`text-2xl md:text-3xl font-bold ${THEME_COLORS.gradientTextUser} mb-5`}>Aviation Authority Edict</h1> {/* Compacted text-3xl/4xl, mb-6 */}
      
      <div className={`space-y-3.5 ${THEME_COLORS.textMedium} text-sm`}> {/* Compacted space-y-4 */}
        <p className={`font-semibold ${THEME_COLORS.textSecondaryAccent}`}>ATTENTION: JetSharing.co is NOT a Part 135 operator, direct air carrier, indirect air carrier, or foreign air carrier. We do NOT operate aircraft. We are a technology platform facilitating P2P connections between passengers and pilots/operators who maintain full operational control. Blockchain tech may be used for verification.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>1. Regulatory Adherence</h2> {/* Compacted text-xl, mt-6, mb-2 */}
        <p>All flights via JetSharing.co must comply with all applicable Federal Aviation Administration (FAA) regulations (or equivalent local aviation authority edicts for non-US airspace). This includes aircraft airworthiness, pilot certification/currency, operational limits, and passenger-carrying rules.</p>

        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>2. Pilot Mandate</h2>
        <p>Pilots listing flights affirm they:</p>
        <ul className="list-disc list-inside ml-3 space-y-0.5 text-xs"> {/* Compacted ml-4, space-y-1 */}
          <li>Hold all necessary licenses, ratings, and medical certificates for the proposed flight.</li>
          <li>Will operate per all applicable FAA (or local equivalent) regulations.</li>
          <li>Have sole operational control and are responsible for all flight planning/execution, including go/no-go decisions.</li>
          <li>Ensure aircraft is airworthy and maintained per regulatory standards.</li>
          <li>Comply with cost-sharing/compensation rules (e.g., FAR 61.113(c) - verify legality) and differentiate from commercial ops (requiring Air Carrier Certificate under FAR Part 119, 121, or 135).</li>
        </ul>
        <p className={`font-semibold ${THEME_COLORS.textSecondaryAccent} mt-1.5 text-xs`}>Pilots are strictly forbidden from offering illegal charter operations.</p>


        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>3. Traveler Cognizance</h2>
        <p>Travelers acknowledge:</p>
        <ul className="list-disc list-inside ml-3 space-y-0.5 text-xs">
          <li>Flights may operate under various FAA parts. Part 91 (private) safety standards differ from commercial Part 135/121.</li>
          <li>They are responsible for understanding the flight nature and conducting due diligence on pilot/aircraft (on-chain verification where available).</li>
          <li>JetSharing.co does not vet pilots beyond its stated verification (potentially on-chain) and makes no safety/legality representations beyond info sharing.</li>
        </ul>
        
        <h2 className={`text-lg font-semibold mt-5 mb-1.5 ${THEME_COLORS.textPrimaryAccent}`}>4. No Air Carrier Provision</h2>
        <p>JetSharing.co offers no air transport services. Our role is solely P2P connection. Travel agreements are strictly between Traveler and Pilot/Operator.</p>

        <p className="mt-5 font-semibold text-xs">By using JetSharing.co, all users agree to these disclaimers and all applicable aviation laws. If unsure, consult aviation legal experts or relevant authorities.</p>

        <p className={`mt-6 text-xs ${THEME_COLORS.textDark}`}>Last Reviewed: {new Date().toLocaleDateString()}</p> {/* Compacted mt-8, text-sm */}
      </div>
    </div>
  );
};

export default FaaDisclaimerPage;
