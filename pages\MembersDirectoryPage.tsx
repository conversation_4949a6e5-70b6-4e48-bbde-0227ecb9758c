
import React, { useState, useMemo } from 'react';
import { UserProfile, UserRole } from '../types';
import { MOCK_USERS, THEME_COLORS } from '../constants';
import UserProfileCard from '../components/UserProfileCard';
import { ChevronDownIcon } from '../components/icons/ChevronDownIcon';


const MembersDirectoryPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState<UserRole | 'ALL'>('ALL');
  const [filterLocation, setFilterLocation] = useState('');

  const filteredUsers = useMemo(() => {
    return MOCK_USERS.filter(user => {
      const nameMatch = user.name.toLowerCase().includes(searchTerm.toLowerCase());
      const roleMatch = filterRole === 'ALL' || user.role === filterRole;
      const locationMatch = !filterLocation || (user.location && user.location.toLowerCase().includes(filterLocation.toLowerCase()));
      return nameMatch && roleMatch && locationMatch;
    });
  }, [searchTerm, filterRole, filterLocation]);

  const commonInputClasses = `w-full p-2.5 text-sm ${THEME_COLORS.bgGlass} rounded-lg border ${THEME_COLORS.borderPrimaryAccent}/50 focus:ring-2 ${THEME_COLORS.ringPrimaryAccent} focus:${THEME_COLORS.borderPrimaryAccent} outline-none transition-all ${THEME_COLORS.textLight} placeholder-[var(--color-text-dark)]`;

  return (
    <div className="space-y-10"> {/* Compacted space-y-12 */}
      <h1 className={`text-3xl font-bold text-center ${THEME_COLORS.gradientTextUser} mb-6`}>JetSharing.co Member Matrix</h1> {/* Compacted text-4xl, mb-8 */}
      
      <section className={`${THEME_COLORS.bgGlass} p-5 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/10 scroll-animation`}> {/* Compacted p-6 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> {/* Compacted gap-6 */}
          <div>
            <label htmlFor="searchTerm" className={`block text-xs font-medium ${THEME_COLORS.textPrimaryAccent} mb-0.5`}>Search by Handle</label> {/* Compacted text-sm, mb-1 */}
            <input
              type="text"
              id="searchTerm"
              placeholder="Enter name/alias..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className={commonInputClasses}
            />
          </div>
          <div className="relative">
            <label htmlFor="filterRole" className={`block text-xs font-medium ${THEME_COLORS.textPrimaryAccent} mb-0.5`}>Filter by Archetype</label>
            <select
              id="filterRole"
              value={filterRole}
              onChange={e => setFilterRole(e.target.value as UserRole | 'ALL')}
              className={`${commonInputClasses} appearance-none pr-7 bg-transparent`}
            >
              <option value="ALL" className="bg-[var(--color-dark-bg)]">All Members</option>
              <option value={UserRole.Pilot} className="bg-[var(--color-dark-bg)]">Pilots</option>
              <option value={UserRole.Traveler} className="bg-[var(--color-dark-bg)]">Travelers</option>
            </select>
            <ChevronDownIcon className={`w-4 h-4 ${THEME_COLORS.textMedium} absolute right-2.5 top-[calc(50%+4px)] transform -translate-y-1/2 pointer-events-none`} /> {/* Compacted icon size, positioning */}
          </div>
          <div>
            <label htmlFor="filterLocation" className={`block text-xs font-medium ${THEME_COLORS.textPrimaryAccent} mb-0.5`}>Filter by Geo-Node</label>
            <input
              type="text"
              id="filterLocation"
              placeholder="Enter city/sector..."
              value={filterLocation}
              onChange={e => setFilterLocation(e.target.value)}
              className={commonInputClasses}
            />
          </div>
        </div>
      </section>

      <section>
        <h2 className={`text-xl font-bold mb-5 ${THEME_COLORS.textSecondaryAccent}`}> {/* Compacted text-2xl, mb-6 */}
          Displaying {filteredUsers.length} Verified Identities
        </h2>
        {filteredUsers.length > 0 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5"> {/* Compacted gap-8 to gap-5 */}
            {filteredUsers.map(user => (
              <UserProfileCard key={user.id} user={user} />
            ))}
          </div>
        ) : (
          <div className={`text-center py-10 ${THEME_COLORS.bgGlass} rounded-xl`}> {/* Compacted py-12 */}
            <p className={`text-lg ${THEME_COLORS.textMedium}`}>No identities match your query parameters.</p> {/* Compacted text-xl */}
            <p className={`${THEME_COLORS.textPrimaryAccent} mt-1.5 text-sm`}>Refine filters or explore the full member matrix.</p> {/* Compacted mt-2 */}
          </div>
        )}
      </section>
    </div>
  );
};

export default MembersDirectoryPage;
