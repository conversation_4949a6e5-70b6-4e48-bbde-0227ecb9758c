
import React from 'react';
import { MEMBERSHIP_TIERS, THEME_COLORS } from '../constants';
import TierCard from '../components/TierCard';

const MembershipPage: React.FC = () => {
  return (
    <div className="space-y-10"> {/* Compacted space-y-12 */}
      <section className="text-center scroll-animation">
        <h1 className={`text-3xl md:text-4xl font-bold ${THEME_COLORS.gradientTextUser} mb-3`}>JetSharing.co Access Tiers</h1> {/* Compacted text-4xl/5xl, mb-4 */}
        <p className={`text-base md:text-lg ${THEME_COLORS.textMedium} max-w-xl mx-auto`}> {/* Compacted text-lg/xl, max-w-2xl */}
          Elevate your transit experience. Select an access tier to unlock exclusive benefits, advanced matrix filters, and premium support on the JetSharing.co hyperlane.
        </p>
      </section>

      <section className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 items-stretch"> {/* Compacted gap-8 */}
        {MEMBERSHIP_TIERS.map(tier => (
          <TierCard key={tier.id} tier={tier} />
        ))}
      </section>

      <section className={`${THEME_COLORS.bgGlass} p-6 rounded-xl shadow-xl ${THEME_COLORS.shadowPrimary}/20 text-center scroll-animation`}> {/* Compacted p-8 */}
        <h2 className={`text-2xl font-bold mb-3 ${THEME_COLORS.textSecondaryAccent}`}>Why Ascend Tiers?</h2> {/* Compacted text-3xl, mb-4 */}
        <div className={`grid md:grid-cols-3 gap-5 ${THEME_COLORS.textMedium} text-xs`}> {/* Compacted gap-6, text-sm */}
          <div>
            <h4 className={`text-base font-semibold mb-1 ${THEME_COLORS.textPrimaryAccent}`}>Priority Access</h4> {/* Compacted text-lg */}
            <p>Be first on the vector for new empty leg drops and exclusive flight opportunities.</p>
          </div>
          <div>
            <h4 className={`text-base font-semibold mb-1 ${THEME_COLORS.textPrimaryAccent}`}>Augmented Tools</h4>
            <p>Utilize enhanced matrix filters, flight path tracking, and personalized alpha alerts.</p>
          </div>
          <div>
            <h4 className={`text-base font-semibold mb-1 ${THEME_COLORS.textPrimaryAccent}`}>Singularity Support</h4>
            <p>Receive dedicated assistance from our support DAO for a frictionless experience.</p>
          </div>
        </div>
        <div className="mt-6"> {/* Compacted mt-8 */}
            <p className={`${THEME_COLORS.textDark} text-2xs`}>Crypto payments are routed via secure L2 networks. DYOR on crypto transactions. WAGMI.</p> {/* Compacted text-sm */}
        </div>
      </section>
    </div>
  );
};

export default MembershipPage;
