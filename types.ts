
export interface Flight {
  id: string;
  pilotId: string;
  pilotName: string;
  pilotRating: number;
  aircraftType: string;
  departureAirport: string; // ICAO code
  arrivalAirport: string; // ICAO code
  departureTime: string; // ISO 8601 string
  arrivalTime: string; // ISO 8601 string
  availableSeats: number;
  pricePerSeatUSD: number;
  pricePerSeatETH?: number;
  isPetFriendly: boolean;
  description?: string;
  verifiedPilot?: boolean;
}

export enum UserRole {
  Traveler = 'TRAVELER',
  Pilot = 'PILOT',
}

export interface UserProfile {
  id: string;
  name: string;
  email?: string; // Added for login
  role: UserRole;
  walletAddress?: string;
  profilePictureUrl?: string;
  location?: string;
  memberSince: string; // ISO 8601 string
  bio?: string;
  badges: BadgeType[];
  // Traveler specific
  preferences?: string[];
  pastTrips?: Flight[]; // Simplified
  savedSearches?: any[];
  // Pilot specific
  aircraftDetails?: AircraftDetails[];
  certifications?: string[];
  flightHistory?: Flight[]; // Simplified
  pilotRating: number;
}

export interface AircraftDetails {
  make: string;
  model: string;
  registration: string;
}

export enum BadgeType {
  VerifiedPilot = 'Verified Pilot',
  CryptoNative = 'Crypto Native',
  PetFriendlyHost = 'Pet-Friendly Host',
  TopRated = 'Top Rated',
  EarlyAdopter = 'Early Adopter'
}

export interface MembershipTier {
  id: string;
  name: string;
  priceUSD: number | string; // Can be "Free"
  priceCrypto?: { amount: number; currency: 'ETH' | 'BTC' };
  features: string[];
  isPopular?: boolean;
  glowColor: string; // e.g., 'cyan', 'green', 'purple'
}

export interface WalletState {
  isConnected: boolean;
  address: string | null;
  balance: { eth: number; btc: number } | null; // Mock balance
}

export interface WalletContextType extends WalletState {
  connectWallet: () => void;
  disconnectWallet: () => void;
}

// --- Auth Context Types ---
export interface AuthState {
  isAuthenticated: boolean;
  currentUser: UserProfile | null;
}

export interface AuthContextType extends AuthState {
  login: (email: string, password?: string) => Promise<UserProfile | null>; // Password optional for mock
  logout: () => void;
}