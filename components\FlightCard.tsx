
import React from 'react';
import { Link } from 'react-router-dom';
import { Flight } from '../types';
import { ROUTES, THEME_COLORS } from '../constants';
import { MapPinIcon } from './icons/MapPinIcon';
import { CalendarDaysIcon } from './icons/CalendarDaysIcon';
import { UserGroupIcon } from './icons/UserGroupIcon';
import { PetIcon } from './icons/PetIcon';
import { SparklesIcon } from './icons/SparklesIcon';

interface FlightCardProps {
  flight: Flight;
}

const FlightCard: React.FC<FlightCardProps> = ({ flight }) => {
  const departureDate = new Date(flight.departureTime).toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
  const departureTime = new Date(flight.departureTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });

  return (
    <div className={`${THEME_COLORS.bgGlass} rounded-xl shadow-lg hover:shadow-2xl hover:${THEME_COLORS.shadowPrimary} transition-all duration-300 p-4 flex flex-col justify-between scroll-animation`}> {/* Compacted p-6 to p-4 */}
      <div>
        <div className="flex justify-between items-start mb-2"> {/* Compacted mb-3 */}
          <h3 className={`text-lg font-bold ${THEME_COLORS.gradientTextUser}`}>{flight.aircraftType}</h3> {/* Compacted text-xl */}
          {flight.isPetFriendly && <PetIcon className={`w-5 h-5 ${THEME_COLORS.textSecondaryAccent}`} />} {/* Compacted w-6 h-6 */}
        </div>
        <div className="mb-3 space-y-1.5 text-xs"> {/* Compacted mb-4, space-y-2, text-sm */}
          <div className="flex items-center">
            <MapPinIcon className={`w-4 h-4 mr-1.5 ${THEME_COLORS.textMedium}`} /> {/* Compacted w-5 h-5, mr-2 */}
            <span className={THEME_COLORS.textLight}>{flight.departureAirport} to {flight.arrivalAirport}</span>
          </div>
          <div className="flex items-center">
            <CalendarDaysIcon className={`w-4 h-4 mr-1.5 ${THEME_COLORS.textMedium}`} />
            <span className={THEME_COLORS.textLight}>{departureDate} at {departureTime}</span>
          </div>
          <div className="flex items-center">
            <UserGroupIcon className={`w-4 h-4 mr-1.5 ${THEME_COLORS.textMedium}`} />
            <span className={THEME_COLORS.textLight}>{flight.availableSeats} seats available</span>
          </div>
        </div>
        <Link to={`${ROUTES.USER_PROFILE}/${flight.pilotId}`} className="group inline-flex items-center mb-2"> {/* Compacted mb-3 */}
          <span className={`text-xs ${THEME_COLORS.textMedium} group-hover:${THEME_COLORS.textPrimaryAccent} transition-colors`}>Pilot: {flight.pilotName} ({flight.pilotRating} ★)</span>
          {flight.verifiedPilot && <SparklesIcon className={`w-3 h-3 ml-1 ${THEME_COLORS.textPrimaryAccent}`} />} {/* Compacted w-4 h-4 */}
        </Link>
        {flight.description && <p className={`text-xs ${THEME_COLORS.textDark} mb-3 h-9 overflow-hidden text-ellipsis`}>{flight.description}</p>} {/* Compacted h-10, mb-4 */}
      </div>
      <div className="mt-auto">
        <div className="flex justify-between items-center mb-3"> {/* Compacted mb-4 */}
          <div className={`text-xl font-bold ${THEME_COLORS.textPrimaryAccent}`}> {/* Compacted text-2xl */}
            ${flight.pricePerSeatUSD}
            {flight.pricePerSeatETH && <span className={`text-xs ml-1 ${THEME_COLORS.textMedium}`}>/ {flight.pricePerSeatETH} ETH</span>}
          </div>
          <span className={`text-xs ${THEME_COLORS.textDark}`}>per seat</span>
        </div>
        <button className={`w-full ${THEME_COLORS.gradientBgButton} text-white font-bold py-2.5 px-3 border border-transparent rounded-lg shadow-md hover:shadow-lg ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} text-sm`}> {/* Compacted py-3, text size */}
          View Details / Book Seat
        </button>
      </div>
    </div>
  );
};

export default FlightCard;
