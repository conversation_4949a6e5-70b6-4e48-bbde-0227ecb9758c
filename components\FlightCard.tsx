
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Flight } from '../types';
import { ROUTES, THEME_COLORS } from '../constants';
import { MapPinIcon } from './icons/MapPinIcon';
import { CalendarDaysIcon } from './icons/CalendarDaysIcon';
import { UserGroupIcon } from './icons/UserGroupIcon';
import { PetIcon } from './icons/PetIcon';
import { SparklesIcon } from './icons/SparklesIcon';
import { Plane, Star, Zap, Shield } from 'lucide-react';
import AnimatedButton from './AnimatedButton';

interface FlightCardProps {
  flight: Flight;
}

const FlightCard: React.FC<FlightCardProps> = ({ flight }) => {
  const [isHovered, setIsHovered] = useState(false);
  const departureDate = new Date(flight.departureTime).toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
  const departureTime = new Date(flight.departureTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });

  const cardVariants = {
    initial: {
      scale: 1,
      rotateY: 0,
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
    },
    hover: {
      scale: 1.05,
      rotateY: 5,
      boxShadow: '0 20px 40px rgba(157, 0, 255, 0.3), 0 0 30px rgba(255, 0, 60, 0.2)',
      transition: { type: 'spring', stiffness: 300, damping: 20 }
    }
  };

  const contentVariants = {
    initial: { opacity: 0.8 },
    hover: {
      opacity: 1,
      transition: { duration: 0.3 }
    }
  };

  const planeVariants = {
    initial: { x: 0, rotate: 0 },
    hover: {
      x: 10,
      rotate: 15,
      transition: { type: 'spring', stiffness: 400, damping: 10 }
    }
  };

  const shimmerVariants = {
    initial: { x: '-100%', opacity: 0 },
    hover: {
      x: '100%',
      opacity: [0, 1, 0],
      transition: { duration: 0.8, ease: 'easeInOut' }
    }
  };

  return (
    <motion.div
      className="relative overflow-hidden rounded-xl p-6 flex flex-col justify-between scroll-animation group perspective-1000"
      style={{
        background: 'linear-gradient(135deg, rgba(25, 10, 40, 0.9) 0%, rgba(18, 5, 30, 0.8) 100%)',
        backdropFilter: 'blur(20px)',
        border: '1px solid rgba(157, 0, 255, 0.2)',
      }}
      variants={cardVariants}
      initial="initial"
      whileHover="hover"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
        variants={shimmerVariants}
        initial="initial"
        animate={isHovered ? 'hover' : 'initial'}
      />

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-purple-400 rounded-full opacity-60"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.6, 1, 0.6],
            }}
            transition={{
              duration: 2 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <motion.div variants={contentVariants} initial="initial" animate={isHovered ? 'hover' : 'initial'}>
        <div className="flex justify-between items-start mb-4 relative z-10">
          <div className="flex items-center gap-3">
            <motion.div variants={planeVariants}>
              <Plane className="w-6 h-6 text-purple-400" />
            </motion.div>
            <h3 className="text-xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
              {flight.aircraftType}
            </h3>
          </div>
          <div className="flex items-center gap-2">
            {flight.isPetFriendly && (
              <motion.div
                whileHover={{ scale: 1.2, rotate: 10 }}
                className="p-1 rounded-full bg-pink-500/20"
              >
                <PetIcon className="w-4 h-4 text-pink-400" />
              </motion.div>
            )}
            {flight.verifiedPilot && (
              <motion.div
                whileHover={{ scale: 1.2, rotate: -10 }}
                className="p-1 rounded-full bg-green-500/20"
              >
                <Shield className="w-4 h-4 text-green-400" />
              </motion.div>
            )}
          </div>
        </div>
        <div className="mb-4 space-y-3 relative z-10">
          <motion.div
            className="flex items-center gap-3 p-2 rounded-lg bg-purple-500/10 backdrop-blur-sm"
            whileHover={{ x: 5 }}
            transition={{ type: 'spring', stiffness: 400 }}
          >
            <MapPinIcon className="w-4 h-4 text-purple-400" />
            <span className="text-gray-200 font-medium">
              {flight.departureAirport} → {flight.arrivalAirport}
            </span>
          </motion.div>

          <motion.div
            className="flex items-center gap-3 p-2 rounded-lg bg-pink-500/10 backdrop-blur-sm"
            whileHover={{ x: 5 }}
            transition={{ type: 'spring', stiffness: 400, delay: 0.1 }}
          >
            <CalendarDaysIcon className="w-4 h-4 text-pink-400" />
            <span className="text-gray-200 font-medium">
              {departureDate} at {departureTime}
            </span>
          </motion.div>

          <motion.div
            className="flex items-center gap-3 p-2 rounded-lg bg-red-500/10 backdrop-blur-sm"
            whileHover={{ x: 5 }}
            transition={{ type: 'spring', stiffness: 400, delay: 0.2 }}
          >
            <UserGroupIcon className="w-4 h-4 text-red-400" />
            <span className="text-gray-200 font-medium">
              {flight.availableSeats} seats available
            </span>
          </motion.div>
        </div>
        <Link to={`${ROUTES.USER_PROFILE}/${flight.pilotId}`} className="group">
          <motion.div
            className="flex items-center gap-2 p-3 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm mb-4 relative z-10"
            whileHover={{ scale: 1.02 }}
            transition={{ type: 'spring', stiffness: 400 }}
          >
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-400" />
              <span className="text-gray-200 font-medium">
                {flight.pilotName} ({flight.pilotRating} ★)
              </span>
            </div>
            {flight.verifiedPilot && (
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              >
                <Zap className="w-4 h-4 text-yellow-400" />
              </motion.div>
            )}
          </motion.div>
        </Link>

        {flight.description && (
          <motion.p
            className="text-sm text-gray-300 mb-4 p-3 rounded-lg bg-gray-500/10 backdrop-blur-sm relative z-10"
            initial={{ opacity: 0.7 }}
            whileHover={{ opacity: 1 }}
          >
            {flight.description}
          </motion.p>
        )}
      </motion.div>
      <div className="mt-auto relative z-10">
        <motion.div
          className="flex justify-between items-center mb-4 p-3 rounded-lg bg-gradient-to-r from-green-500/10 to-blue-500/10 backdrop-blur-sm"
          whileHover={{ scale: 1.02 }}
        >
          <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
            ${flight.pricePerSeatUSD}
            {flight.pricePerSeatETH && (
              <span className="text-sm ml-2 text-gray-400">
                / {flight.pricePerSeatETH} ETH
              </span>
            )}
          </div>
          <span className="text-sm text-gray-400">per seat</span>
        </motion.div>

        <AnimatedButton
          variant="holographic"
          size="lg"
          fullWidth
          glowEffect
          icon={<Plane className="w-4 h-4" />}
          iconPosition="right"
        >
          Book This Flight
        </AnimatedButton>
      </div>
    </motion.div>
  );
};

export default FlightCard;
