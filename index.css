/* Enhanced WEB3 Flight Platform Styles */

/* Modern CSS Variables */
:root {
  /* Core Brand Colors */
  --color-primary-purple: #9D00FF;
  --color-primary-purple-glow: rgba(157, 0, 255, 0.5);
  --color-secondary-red: #FF003C;
  --color-secondary-red-glow: rgba(255, 0, 60, 0.5);
  --color-gradient-pink: #F02D7A;
  --color-accent-cyan: #00FFFF;
  --color-accent-gold: #FFD700;

  /* Background Colors */
  --color-dark-bg: #08030D;
  --color-glass-bg: rgba(18, 5, 30, 0.65);
  --color-glass-border: rgba(157, 0, 255, 0.3);
  --color-card-bg: rgba(25, 10, 40, 0.8);

  /* Text Colors */
  --color-text-light: #F0F0F0;
  --color-text-medium: #B0B0B0;
  --color-text-dark: #707070;

  /* Input Colors */
  --color-input-bg: rgba(25, 10, 40, 0.7);
  --color-input-border: rgba(157, 0, 255, 0.35);
  --color-input-focus-ring: var(--color-primary-purple);

  /* Animation Variables */
  --animation-speed-fast: 0.2s;
  --animation-speed-normal: 0.3s;
  --animation-speed-slow: 0.6s;
  --animation-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Enhanced Body Styles */
body {
  font-family: 'Exo 2', sans-serif;
  background: #0a0015;
  color: var(--color-text-light);
  overflow-x: hidden;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Orbitron', sans-serif;
  font-weight: 700;
  letter-spacing: 0.02em;
}

/* Glassmorphism Enhanced */
.glassmorphic {
  background: var(--color-glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--color-glass-border);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced Card Styles */
.card-enhanced {
  background: var(--color-card-bg);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(157, 0, 255, 0.2);
  border-radius: 16px;
  transition: all var(--animation-speed-normal) var(--animation-ease);
  position: relative;
  overflow: hidden;
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(157, 0, 255, 0.1),
    transparent
  );
  transition: left 0.8s ease;
}

.card-enhanced:hover::before {
  left: 100%;
}

.card-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: var(--color-primary-purple);
  box-shadow: 
    0 20px 40px rgba(157, 0, 255, 0.3),
    0 0 30px rgba(255, 0, 60, 0.2);
}

/* Animated Gradients */
.bg-gradient-animated {
  background: linear-gradient(
    45deg,
    var(--color-secondary-red),
    var(--color-gradient-pink),
    var(--color-primary-purple),
    var(--color-accent-cyan)
  );
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Text Gradients Enhanced */
.text-gradient-theme-user {
  background: linear-gradient(
    135deg,
    var(--color-primary-purple),
    var(--color-gradient-pink),
    var(--color-secondary-red),
    var(--color-accent-cyan)
  );
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 6s ease infinite;
}

.text-gradient-holographic {
  background: linear-gradient(
    45deg,
    #ff0080,
    #ff8c00,
    #40e0d0,
    #da70d6,
    #ff0080
  );
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: holographicShift 3s ease infinite;
}

@keyframes holographicShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Enhanced Button Styles */
.btn-primary-enhanced {
  background: linear-gradient(135deg, var(--color-secondary-red), var(--color-primary-purple));
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  color: white;
  position: relative;
  overflow: hidden;
  transition: all var(--animation-speed-normal) var(--animation-ease);
  cursor: pointer;
}

.btn-primary-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.btn-primary-enhanced:hover::before {
  left: 100%;
}

.btn-primary-enhanced:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 10px 25px rgba(157, 0, 255, 0.4),
    0 0 20px rgba(255, 0, 60, 0.3);
}

.btn-primary-enhanced:active {
  transform: translateY(0) scale(0.98);
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(157, 0, 255, 0.3);
  }
  50% { 
    box-shadow: 0 0 40px rgba(157, 0, 255, 0.6), 0 0 60px rgba(255, 0, 60, 0.3);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Scroll Animations Enhanced */
.scroll-animation {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all var(--animation-speed-slow) var(--animation-ease);
}

.scroll-animation-active {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Stagger Animation */
.stagger-animation {
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--animation-speed-normal) var(--animation-ease);
}

.stagger-animation-active {
  opacity: 1;
  transform: translateY(0);
}

/* Loading Skeleton */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-dark-bg);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(var(--color-primary-purple), var(--color-secondary-red));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(var(--color-secondary-red), var(--color-primary-purple));
}

/* Particle Background */
.particle-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .card-enhanced:hover {
    transform: translateY(-4px) scale(1.01);
  }
  
  .btn-primary-enhanced:hover {
    transform: translateY(-1px) scale(1.02);
  }
}

/* Focus States */
*:focus {
  outline: 2px solid var(--color-primary-purple);
  outline-offset: 2px;
}

/* Selection */
::selection {
  background: var(--color-primary-purple);
  color: white;
}
