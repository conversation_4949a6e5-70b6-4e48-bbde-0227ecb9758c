
import React from 'react';
import { Link } from 'react-router-dom';
import { ROUTES, THEME_COLORS, NAV_LINKS_CONFIG } from '../constants';

const NotFoundPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[55vh] text-center px-4"> {/* Compacted min-h */}
      <h1 className={`text-5xl md:text-8xl font-bold ${THEME_COLORS.textSecondaryAccent} mb-3 animate-pulse`}>404</h1> {/* Compacted text-6xl/9xl, mb-4 */}
      <h2 className={`text-xl md:text-3xl font-semibold ${THEME_COLORS.textPrimaryAccent} mb-5`}>Vector Not Found</h2> {/* Compacted text-2xl/4xl, mb-6 */}
      <p className={`text-base ${THEME_COLORS.textMedium} mb-6 max-w-sm`}> {/* Compacted text-lg, mb-8, max-w-md */}
        Oops! The requested data stream is offline or the vector has shifted.
      </p>
      <div className="space-x-3"> {/* Compacted space-x-4 */}
        <Link
          to={ROUTES.HOME}
          className={`${THEME_COLORS.gradientBgButton} text-white font-bold py-2.5 px-5 rounded-lg shadow-lg hover:shadow-xl ${THEME_COLORS.shadowPrimary} transition-all duration-300 ${THEME_COLORS.buttonGlow} text-sm`} // Compacted py-3, px-6
        >
          Return to Nexus
        </Link>
        <Link
          to={ROUTES.SEARCH_FLIGHTS}
          className={`bg-transparent hover:bg-[var(--color-primary-purple)]/20 ${THEME_COLORS.textPrimaryAccent} font-bold py-2.5 px-5 rounded-lg border-2 ${THEME_COLORS.borderPrimaryAccent} shadow-md hover:shadow-lg ${THEME_COLORS.shadowSecondary} transition-all duration-300 ${THEME_COLORS.buttonGlowSecondary} text-sm`}
        >
          {NAV_LINKS_CONFIG.findFlights}
        </Link>
      </div>
      <div className="mt-12"> {/* Compacted mt-16 */}
        <svg viewBox="0 0 100 20" className="w-56 h-auto text-gray-700"> {/* Compacted w-64 */}
            <defs>
                <linearGradient id="gradientNotFound" x1="0" y1="0" x2="1" y2="0">
                <stop offset="0%" stopColor="var(--color-secondary-red)" /> 
                <stop offset="50%" stopColor="var(--color-gradient-pink)" /> 
                <stop offset="100%" stopColor="var(--color-primary-purple)" /> 
                </linearGradient>
                <mask id="maskNotFound">
                <polyline id="curveNotFound" points="0,15 10,10 20,15 30,10 40,15 50,10 60,15 70,10 80,15 90,10 100,15" fill="transparent" stroke="#fff" strokeWidth="1.5"/> {/* Compacted strokeWidth */}
                </mask>
            </defs>
            <rect x="0" y="0" width="100" height="20" fill="url(#gradientNotFound)" mask="url(#maskNotFound)"/>
             <style>
                {`
                #curveNotFound {
                    animation: waveNotFound 1.8s linear infinite; /* Faster animation */
                    stroke-dasharray: 15; /* Adjusted for new strokeWidth */
                }
                @keyframes waveNotFound {
                    0% { stroke-dashoffset: 0; }
                    100% { stroke-dashoffset: -30; } /* Adjusted for new dasharray */
                }
                `}
            </style>
        </svg>
        <p className={`text-xs ${THEME_COLORS.textDark} mt-1.5`}>Recalibrate your search or check the hyperlane address.</p> {/* Compacted text-sm, mt-2 */}
      </div>
    </div>
  );
};

export default NotFoundPage;
