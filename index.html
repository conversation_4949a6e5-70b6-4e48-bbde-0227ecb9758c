
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JetSharing.co - Future of Private Aviation</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Exo+2:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
  <style>
    :root {
      --color-primary-purple: #9D00FF;
      --color-primary-purple-glow: rgba(157, 0, 255, 0.5);
      --color-secondary-red: #FF003C;
      --color-secondary-red-glow: rgba(255, 0, 60, 0.5);
      --color-gradient-pink: #F02D7A; /* Midpoint for gradients */

      --color-dark-bg: #08030D; /* Very dark, almost black with purple hint */
      --color-glass-bg: rgba(18, 5, 30, 0.65); /* Darker purple tint for glassmorphism */
      --color-glass-border: rgba(157, 0, 255, 0.3);

      --color-text-light: #F0F0F0;
      --color-text-medium: #B0B0B0;
      --color-text-dark: #707070;

      --color-input-bg: rgba(25, 10, 40, 0.7);
      --color-input-border: rgba(157, 0, 255, 0.35);
      --color-input-focus-ring: var(--color-primary-purple);
    }

    body {
      font-family: 'Exo 2', sans-serif;
      background-color: var(--color-dark-bg);
      color: var(--color-text-light);
    }
    h1, h2, h3, h4, h5, h6 {
      font-family: 'Orbitron', sans-serif;
    }

    .glassmorphic {
      background: var(--color-glass-bg);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid var(--color-glass-border);
    }

    /* New Theme Text & Borders */
    .text-theme-primary { color: var(--color-primary-purple); }
    .text-theme-secondary { color: var(--color-secondary-red); }
    .border-theme-primary { border-color: var(--color-primary-purple); }
    .border-theme-secondary { border-color: var(--color-secondary-red); }
    .ring-theme-primary { ring-color: var(--color-primary-purple); }
    .ring-theme-secondary { ring-color: var(--color-secondary-red); }

    /* Gradients */
    .bg-gradient-theme-button {
      background-image: linear-gradient(to right, var(--color-secondary-red) 0%, var(--color-gradient-pink) 50%, var(--color-primary-purple) 100%);
    }
     .bg-gradient-theme-button:hover {
      background-image: linear-gradient(to right, var(--color-secondary-red) 0%, var(--color-primary-purple) 100%); /* Simpler hover or brightness change */
    }

    .text-gradient-theme-user {
      background: linear-gradient(to right, var(--color-primary-purple), var(--color-gradient-pink), var(--color-secondary-red));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
     .text-gradient-theme-subtle {
      background: linear-gradient(to right, var(--color-text-medium), var(--color-text-light));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }


    /* Glows */
    .shadow-theme-primary-glow { box-shadow: 0 0 15px 3px var(--color-primary-purple-glow); }
    .shadow-theme-secondary-glow { box-shadow: 0 0 15px 3px var(--color-secondary-red-glow); }
    
    .button-glow-theme:hover {
      box-shadow: 0 0 20px 5px var(--color-primary-purple-glow), 0 0 8px 2px var(--color-secondary-red-glow) inset;
    }
     .button-glow-theme-secondary:hover {
      box-shadow: 0 0 15px 3px var(--color-secondary-red-glow), 0 0 5px 1px var(--color-primary-purple-glow) inset;
    }


    .scroll-animation {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    }
    .scroll-animation-active {
      opacity: 1;
      transform: translateY(0);
    }
    
    /* Custom input styling for the new theme */
    input[type="text"], input[type="email"], input[type="password"], input[type="number"], input[type="datetime-local"], input[type="date"], textarea, select {
        background-color: var(--color-input-bg) !important;
        border-color: var(--color-input-border) !important;
        color: var(--color-text-light) !important;
    }
    input[type="text"]::placeholder, input[type="email"]::placeholder, input[type="password"]::placeholder, input[type="number"]::placeholder, textarea::placeholder {
        color: var(--color-text-dark) !important;
    }
    input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus, input[type="number"]:focus, input[type="datetime-local"]:focus, input[type="date"]:focus, textarea:focus, select:focus {
        border-color: var(--color-primary-purple) !important;
        box-shadow: 0 0 0 2px var(--color-primary-purple-glow) !important;
    }
    select option {
      background-color: var(--color-dark-bg) !important;
      color: var(--color-text-light) !important;
    }

    /* Checkbox styling */
    .form-checkbox-custom {
      appearance: none;
      -webkit-appearance: none;
      height: 1.5em;
      width: 1.5em;
      background-color: var(--color-input-bg);
      border: 1px solid var(--color-input-border);
      border-radius: 0.25em;
      display: inline-block;
      position: relative;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    }
    .form-checkbox-custom:checked {
      background-color: var(--color-primary-purple);
      border-color: var(--color-secondary-red);
    }
    .form-checkbox-custom:checked::after {
      content: '✓';
      font-size: 1.2em;
      color: var(--color-dark-bg); /* Checkmark color */
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    /* Ensure canvas is behind hero content */
    .hero-section-container {
      position: relative;
      z-index: 1; /* Keep content above canvas */
    }
    #hero-canvas-animation {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0; /* Behind hero content */
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body>
  <noscript>You need to enable JavaScript to run this app. On-chain features await!</noscript>
  <div id="root"></div>
  <script>
    // IntersectionObserver to add 'scroll-animation-active' class
    const jsScrollObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('scroll-animation-active');
        }
      });
    }, { threshold: 0.1 });

    function observeElements(elements) {
      elements.forEach(el => {
        if (el.nodeType === Node.ELEMENT_NODE && el.classList.contains('scroll-animation') && !el.classList.contains('scroll-animation-active')) {
          jsScrollObserver.observe(el);
        }
      });
    }

    function unobserveElements(elements) {
      elements.forEach(el => {
        if (el.nodeType === Node.ELEMENT_NODE && el.classList.contains('scroll-animation')) {
          jsScrollObserver.unobserve(el);
        }
      });
    }

    const appMutationObserver = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              observeElements([node]);
              if (typeof node.querySelectorAll === 'function') {
                observeElements(node.querySelectorAll('.scroll-animation'));
              }
            }
          });
          mutation.removedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              unobserveElements([node]);
              if (typeof node.querySelectorAll === 'function') {
                unobserveElements(node.querySelectorAll('.scroll-animation'));
              }
            }
          });
        }
      }
    });

    document.addEventListener('DOMContentLoaded', () => {
      const rootElement = document.getElementById('root');
      if (rootElement) {
        observeElements(rootElement.querySelectorAll('.scroll-animation'));
        appMutationObserver.observe(rootElement, { childList: true, subtree: true });
      }
    });
  </script>
<script type="module" src="/index.tsx"></script>
</body>
</html>
