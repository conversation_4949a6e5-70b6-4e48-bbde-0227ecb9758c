import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronUp, Rocket } from 'lucide-react';

interface ScrollToTopProps {
  threshold?: number;
  variant?: 'default' | 'rocket';
}

const ScrollToTop: React.FC<ScrollToTopProps> = ({ 
  threshold = 300,
  variant = 'rocket'
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > threshold) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, [threshold]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const buttonVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0,
      rotate: -180,
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      rotate: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 20,
      }
    },
    hover: {
      scale: 1.1,
      rotate: variant === 'rocket' ? 15 : 0,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 10,
      }
    },
    tap: {
      scale: 0.9,
    }
  };

  const rocketVariants = {
    initial: { y: 0 },
    hover: { 
      y: [-2, -8, -2],
      transition: {
        duration: 0.6,
        repeat: Infinity,
        ease: 'easeInOut',
      }
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.button
          className="fixed bottom-8 right-8 z-50 p-4 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900"
          variants={buttonVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          whileHover="hover"
          whileTap="tap"
          onClick={scrollToTop}
          style={{
            background: 'linear-gradient(135deg, #9D00FF, #FF003C)',
            boxShadow: '0 0 20px rgba(157, 0, 255, 0.5)',
          }}
        >
          {variant === 'rocket' ? (
            <motion.div
              variants={rocketVariants}
              initial="initial"
              whileHover="hover"
            >
              <Rocket className="w-6 h-6" />
            </motion.div>
          ) : (
            <ChevronUp className="w-6 h-6" />
          )}
          
          {/* Particle trail effect */}
          <div className="absolute inset-0 overflow-hidden rounded-full pointer-events-none">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white rounded-full opacity-60"
                style={{
                  left: '50%',
                  top: '50%',
                }}
                animate={{
                  x: [0, Math.random() * 20 - 10],
                  y: [0, Math.random() * 20 - 10],
                  opacity: [0.6, 0],
                  scale: [1, 0],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>
        </motion.button>
      )}
    </AnimatePresence>
  );
};

export default ScrollToTop;
